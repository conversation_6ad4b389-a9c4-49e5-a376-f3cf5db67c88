import '../config/qr_sting.dart';
import '../util/common_util.dart';
import '../util/qr_log.dart';

class QrCameraResult {
  /// fromJson
  QrCameraResult.fromJson(Map<String, dynamic> json) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': 'QrCameraResult from json', 'data': '$json'});
    if (json.containsKey('retData')) {
      final Map<String, dynamic> map =
          CommonUtil.convertType<Map<dynamic, dynamic>>(
              json['retData'], <dynamic, dynamic>{}).cast<String, dynamic>();
      retData = QrCameraResultData.fromJson(map);
    }
  }

  /// retData
  QrCameraResultData? retData;
}

class QrCameraResultData {
  QrCameraResultData.fromJson(Map<String, dynamic> json) {
    if (json.containsKey('data')) {
      data = CommonUtil.convertType<String?>(json['data'], null);
    }
  }
  String? data;
}
