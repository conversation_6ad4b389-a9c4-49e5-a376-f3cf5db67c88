package com.example.demo.qr.tag;


import java.lang.ref.WeakReference;

public abstract class UpScanBaseHandler {
    protected WeakReference<UpScanControlInterface> upScanControlInterface;

    public void setUpScanInterface(UpScanControlInterface upScanInterface) {
        this.upScanControlInterface = new WeakReference<>(upScanInterface);
    }

    protected UpScanControlInterface getControlInterface() {
        if (upScanControlInterface == null) return null;
        UpScanControlInterface controlInterface = upScanControlInterface.get();

        // 检查Activity是否已销毁
        if (controlInterface instanceof android.app.Activity) {
            android.app.Activity activity = (android.app.Activity) controlInterface;
            if (activity.isFinishing() || activity.isDestroyed()) {
                return null;
            }
        }

        return controlInterface;
    }

    public boolean canHandle(String code) {
        return true;
    }

    public abstract void doHandle(String code, UpScanResultSource source);

    private UpScanBaseHandler nextHandler;

    public void setNextHandler(UpScanBaseHandler handler) {
        this.nextHandler = handler;
    }

    public void handle(String code, UpScanResultSource source) {
        if (canHandle(code)) {
            doHandle(code, source);
            return;
        }
        if (nextHandler != null) {
            nextHandler.handle(code, source);
        }
    }
}