import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:resource/resource.dart';
import 'package:scan/config/constant.dart';
import 'package:scan/config/qr_sting.dart';
import 'package:scan/logic/storage.dart';
import 'package:scan/util/qr_log.dart';

import '../model/qr_scan_common_resource_result.dart';
import 'common_util.dart';

class WhiteCodeUtils {
// 静态变量指向自身
  static final WhiteCodeUtils _instance = WhiteCodeUtils._();
  // 私有构造器
  WhiteCodeUtils._();
  static WhiteCodeUtils getInstance() => _instance;

  Future<List?> gainQrWhiteCode(String whiteCode) async {
    //获取本地扫码白名单
    List? whiteList = await QRStorage().readQrWhiteCodeData(whiteCode);
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_gainQrWhiteCode', 'whiteList': whiteList});
    if (whiteList == null || whiteList is! List || whiteList.length < 1) {
      //获取本地resources数据
      if (whiteCode == CONSTANT.synWhiteCode) {
        String jsonStr = await rootBundle
            .loadString("packages/scan/preresource/qrwhitecodesyn.json");
        final Map<String, List<dynamic>> jsonMap =
            CommonUtil.convertType<Map<dynamic, dynamic>>(
                    jsonDecode(jsonStr), <dynamic, dynamic>{})
                .cast<String, List<dynamic>>();
        whiteList = jsonMap["whiteList"];
      } else {
        String jsonStr = await rootBundle
            .loadString("packages/scan/preresource/qrwhitecode.json");
        final Map<String, List<dynamic>> jsonMap =
            CommonUtil.convertType<Map<dynamic, dynamic>>(
                    jsonDecode(jsonStr), <dynamic, dynamic>{})
                .cast<String, List<dynamic>>();
        whiteList = jsonMap["whiteList"];
      }
    }
    return whiteList;
  }

  void gainQrWhiteCodeFromResource(String whiteCode) async {
    try {
      dynamic result =
          await Resource.getCommonRes(whiteCode, 'configAPP', (map) {
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: {'fn': '_gainQrWhiteCodeFromResurce', 'map': map});
        if (map is! Map<String, dynamic>) {
          return;
        }
        final CommonResourceResult whiteCodeResource =
            CommonResourceResult.fromJson(map);
        if (whiteCodeResource.retCode != '000000' ||
            whiteCodeResource.retData is! CommonResourceData) {
          return;
        }
        final CommonResourceData data = whiteCodeResource.retData!;
        final String? link = data.link;
        if (link != null && link.length > 0) {
          QRStorage().saveQrWhiteCodeJson(link, whiteCode);
        }
      });
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': '_gainQrWhiteCodeFromResurce', 'result': result});
    } catch (e) {
      LogHelper.error(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': '_gainQrWhiteCodeFromResurce', 'err': e});
    }
  }
}
