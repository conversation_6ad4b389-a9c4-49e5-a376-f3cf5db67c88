# 海尔智家安全迁移二维码扫码流程（MIGRATE_QR$开头）

```mermaid
flowchart TD
    A[扫码结果 parseScanStr] -->|str 以 MIGRATE_QR$ 开头| B[_parseSecurity]
    B -->|检查网络| B1{网络可用?}
    B1 --否--> T1[Toast: 网络不可用]
    B1 --是--> B2[检查登录状态]
    B2 -->|已登录?| B3{已登录?}
    B3 --否--> T2[Toast: 未登录]
    B3 --是--> B4[解析taskId]
    B4 -->|taskId为空或格式错误| T3[Toast: 解析失败]
    B4 -->|taskId有效| B5[请求安全任务接口]
    B5 -->|接口返回| B6{retCode == 成功?}
    B6 --否--> T4[Toast: 任务信息错误/任务不存在/超限]
    B6 --是--> B7{state == SecurityNormal/ SecurityPause?}
    B7 --否--> T5[Toast: 任务信息错误]
    B7 --是--> C[complete(ApiUrl.QR_SCAN_SECURITY_URL, QRSecurity, data)]
    C --> D[QRScanGotoPageHandler.classificationGotoPage]
    D -->|type == QRSecurity| E[_securityParse]
    E -->|jsonDecode originalStr| F{data为Map?}
    F --否--> T6[Toast: 任务信息错误]
    F --是--> G[registerAndPush(data)]
    %% 终点可根据registerAndPush实际逻辑补充

    %% 错误分支
    style T1 fill:#fdd
    style T2 fill:#fdd
    style T3 fill:#fdd
    style T4 fill:#fdd
    style T5 fill:#fdd
    style T6 fill:#fdd
```

## 说明
- 只处理以MIGRATE_QR$开头的二维码。
- 主要判断条件、接口、Toast内容均已标注。
- registerAndPush(data)为后续业务处理（如弹窗、跳转等）。

