import 'dart:convert';
import 'dart:io';
import 'package:Appinfos/Appinfos.dart';
import 'package:Appinfos/AppinfosModel.dart';
import 'package:scan/service/qr_scan_http_header.dart';
import 'package:scan/util/qr_log.dart';
import 'package:scan/config/qr_sting.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/user.dart';

import '../util/common_util.dart';

enum QRScanSignType {
  none,
  md5,
  sha256,
  sha256_for_zj,
}

class QRScanHttpService {
  static Future<Map<String, dynamic>?> get(String url, QRScanSignType signType,
      {Map<String, dynamic>? param, Map<String, dynamic>? headers}) async {
    //如果没有?号添加?号
    if (url.indexOf('?') < 0) {
      url += '?';
    }
    //读取参数并组成带参数的url
    if (param != null) {
      param.forEach((key, value) {
        url += (key + '=' + value.toString() + '&');
      });
    }
    //截取字符串
    url = url.substring(0, url.length - 1);

    //定义Http客户端
    HttpClient? httpClient;
    //定义Http客户端请求对象
    HttpClientRequest? request;
    //定义Http客户端返回对象
    HttpClientResponse response;

    try {
      //实例化Http客户端
      httpClient = HttpClient();
      //获取请求对象
      request = await httpClient.getUrl(Uri.parse(url));

      // await setHeader(request);
      await setHeader(request, url, signType, param, header: headers);
      //发起请求并返回对象
      response = await request.close();
      //解析出body内容
      String body = await response.transform(utf8.decoder).join();
      //当返回状态为200时表示请求成功
      if (response.statusCode == 200) {
        //Json解码后生成Map对象并返回
        final Map<String, dynamic> resBody =
            CommonUtil.convertType<Map<dynamic, dynamic>>(
                    json.decode(body), <dynamic, dynamic>{})
                .cast<String, dynamic>();
        return resBody;
      } else {
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: {'fn': 'get', 'info': '请求失败...'});
      }
    } finally {
      //关闭请求对象
      if (request != null) request.close();
      //关闭HttpClient对象
      if (httpClient != null) httpClient.close();
    }
    return null;
  }

  static Future<Map<String, dynamic>?> post(String url, QRScanSignType signType,
      {Map<String, dynamic>? param, Map<String, dynamic>? headers}) async {
//定义Http客户端
    HttpClient? httpClient;
    //定义Http客户端请求对象
    HttpClientRequest? request;
    //定义Http客户端返回对象
    HttpClientResponse response;
    try {
      //实例化Http客户端
      httpClient = HttpClient();
      //获取请求对象
      request = await httpClient.postUrl(Uri.parse(url));
      //设置请求头
      // await setHeader(request);
      await setHeader(request, url, signType, param, header: headers);
      //参数编码后添加至请求对象里
      if (param != null) {
        request.add(utf8.encode(json.encode(param)));
      }
      //发起请求并返回对象
      response = await request.close();
      //解析出body内容
      String body = await response.transform(utf8.decoder).join();
      //当返回状态为200时表示请求成功
      if (response.statusCode == 200) {
        final Map<String, dynamic> resBody =
            CommonUtil.convertType<Map<dynamic, dynamic>>(
                    json.decode(body), <dynamic, dynamic>{})
                .cast<String, dynamic>();
        return resBody;
      } else {
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: {'fn': 'post', 'info': '请求失败...'});
      }
    } finally {
      //关闭请求对象
      if (request != null) request.close();
      //关闭HttpClient对象
      if (httpClient != null) httpClient.close();
    }
    return null;
  }

  static Future<void> setHeader(HttpClientRequest request, String url,
      QRScanSignType signType, Map<String, dynamic>? param,
      {Map<String, dynamic>? header}) async {
    if (signType == QRScanSignType.none) {
      request.headers.set(
          HttpHeaders.contentTypeHeader, 'application/json; charset=UTF-8');
    } else if (signType == QRScanSignType.md5) {
      Map<String, dynamic> headersMap =
          await QRScanServerHeader.signHeaderWithBody(param);

      if (header != null) {
        header.forEach((key, value) {
          request.headers.set(key, value as Object);
        });
      }

      headersMap.forEach((key, value) {
        request.headers.set(key, value as Object);
      });
    } else if (signType == QRScanSignType.sha256) {
      Map<String, dynamic> headersMap =
          await QRScanServerHeader.signHeaderWithBodyAndUrl(url, param);

      if (header != null) {
        header.forEach((key, value) {
          request.headers.set(key, value as Object);
        });
      }

      headersMap.forEach((key, value) {
        request.headers.set(key, value as Object);
      });
    } else if (signType == QRScanSignType.sha256_for_zj) {
      Map<String, dynamic> headersMap =
          await QRScanServerHeader.signHeaderWithBodyAndUrlForZJ(url, param);
      final OauthData oauthData = await User.getOauthData();
      headersMap['accountToken'] = oauthData.user_center_access_token;
      if (header != null) {
        header.forEach((String key, dynamic value) {
          request.headers.set(key, value as Object);
        });
      }

      headersMap.forEach((String key, dynamic value) {
        request.headers.set(key, value as Object);
      });
    }
  }

  // 新增请求产品信息接口，请求方式为post
  static Future<Map<String, dynamic>?> postNew(
      String domain, String url, QRScanSignType signType,
      {Map<String, dynamic>? param, Map<String, dynamic>? headers}) async {
    //定义Http客户端
    HttpClient? httpClient;
    //定义Http客户端请求对象
    HttpClientRequest? request;
    //定义Http客户端返回对象
    HttpClientResponse response;
    try {
      //实例化Http客户端
      httpClient = HttpClient();
      //获取请求对象
      final String urlString = domain + url;
      final Uri uri = Uri.parse(urlString);
      request = await httpClient.postUrl(uri);
      final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();
      headers ??= <String, dynamic>{}; //如果headers为空则初始化为空Map
      headers['grayMode'] = appInfo.grayMode;
      //设置请求头
      await setHeader(request, url, signType, param, header: headers);
      //参数编码后添加至请求对象里
      if (param != null) {
        request.add(utf8.encode(json.encode(param)));
      }
      //发起请求并返回对象
      response = await request.close();
      //解析出body内容
      final String body = await response.transform(utf8.decoder).join();
      //当返回状态为200时表示请求成功
      if (response.statusCode == 200) {
        final Map<String, dynamic> resBody =
            CommonUtil.convertType<Map<dynamic, dynamic>>(
                    json.decode(body), <dynamic, dynamic>{})
                .cast<String, dynamic>();
        return resBody;
      } else {
        LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: <String, String>{'fn': 'post', 'info': '请求失败...'},
        );
      }
    } finally {
      //关闭请求对象
      if (request != null) {
        request.close();
      }
      //关闭HttpClient对象
      if (httpClient != null) {
        httpClient.close();
      }
    }
    return null;
  }
}
