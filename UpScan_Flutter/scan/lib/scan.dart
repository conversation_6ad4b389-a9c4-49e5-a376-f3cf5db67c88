library scan;

import 'package:flutter/material.dart';
import 'package:r_scan/r_scan.dart';
import 'package:scan/config/qr_sting.dart';
import 'package:scan/handler/qr_scan_goto_page_handler.dart';
import 'package:scan/handler/qr_scan_string_handler.dart';
import 'package:scan/pages/qr_syn_camera_scan.dart';
import 'package:scan/util/qr_scan_string_util.dart';

import 'config/qr_general_scan_config.dart';
import 'config/qr_scan_type.dart';
import 'handler/qr_general_scan_handler.dart';
import 'handler/qr_syn_handler.dart';
import 'pages/qr_camera_scan.dart';
import 'pages/qr_general_scan.dart';
import 'util/common_util.dart';

/// A Calculator.
class Calculator {
  /// Returns [value] plus 1.
  int addOne(int value) => value + 1;
}

Widget gotoQrScan({Map<String, dynamic>? params}) {
  if (params != null && params.length > 0) {
    final String androidPhotoPath =
        CommonUtil.convertType<String>(params['androidPhotoPath'], '');
    final String desc =
        CommonUtil.convertType<String>(params['description'], '');
    final String showPhoto =
        CommonUtil.convertType<String>(params['showPhoto'], '');
    final String showTakePhoto =
        CommonUtil.convertType<String>(params['showTakePhoto'], '');
    final String showTips =
        CommonUtil.convertType<String>(params['showTips'], '');
    final String isAndroidFromCamera =
        CommonUtil.convertType<String>(params['isAndroidFromCamera'], '0');
    return RSNcanCameraDialog(
      description: StringUtil.isBlankString(desc) ? '' : desc,
      showPhoto: StringUtil.gainBoolValue(showPhoto, true),
      showTakePhoto: StringUtil.gainBoolValue(showTakePhoto, true),
      showTips: StringUtil.gainBoolValue(showTips, true),
      isAndroidFromCamera: StringUtil.isBlankString(isAndroidFromCamera)
          ? '0'
          : isAndroidFromCamera,
      androidPhotoPath:
          StringUtil.isBlankString(androidPhotoPath) ? null : androidPhotoPath,
      scanResult: _scanResultAction,
    );
  } else {
    return RSNcanCameraDialog(
      description: '',
      showPhoto: true,
      showTakePhoto: true,
      showTips: true,
      scanResult: _scanResultAction,
    );
  }
}

Widget gotoSYNScan({Map<String, dynamic>? params}) {
  if (params == null) {
    params = {};
  }
  final String description =
      CommonUtil.convertType<String>(params['description'], '');
  final String showPhoto =
      CommonUtil.convertType<String>(params['showPhoto'], '');
  return RScanSYNCameraDialog(
    description: StringUtil.isBlankString(description)
        ? QRContentString.QR_SCAN_SYN_CONTENT
        : description,
    showPhoto: StringUtil.gainBoolValue(showPhoto, true),
    scanResult: _synScanResultAction,
  );
}

Widget gotoQrGeneralScan({
  String? scanTitle,
  String? scanContent,
  String? scanError = QRScanErrorString.QR_NO_PARSE_PROMPT,
  String? scanRules,
  String? btn1_Title,
  String? btn1_Link,
  String? btn2_Title,
  String? btn2_Link,
  bool? isShowAlbum,
  String highLightContent = '',
  String showDefaultIcon = 'false',
}) {
  QRScanParametersConfig config = QRScanParametersConfig(
    scanTitle: scanTitle,
    scanContent: scanContent,
    scanError: scanError,
    scanRules: scanRules,
    btn1_Title: btn1_Title,
    btn1_Link: btn1_Link,
    btn2_Title: btn2_Title,
    btn2_Link: btn2_Link,
    isShowAlbum: isShowAlbum,
    highLightContent: highLightContent,
    showDefaultIcon: showDefaultIcon,
  );
  return QRGeneralScanPage(
    config: config,
    scanResult: _generalscanResultAction,
  );
}

void _scanResultAction(List<RScanResult> value, List? whiteList,
    QRScanSourceType type, int milliseconds, VoidCallback popAction) {
  QRScanStringHandler().parseScanStr(value, whiteList, type, milliseconds,
      (result, type, originalStr) {
    QRScanGotoPageHandler()
        .classificationGotoPage(result, originalStr, type, popAction);
  });
}

void _generalscanResultAction(List<RScanResult> value,
    QRScanParametersConfig config, QRGeneralScanBackFunc popAction) {
  QRGeneralScanHandler().handleScanResult(value, config, popAction);
}

void _synScanResultAction(
    List<RScanResult> value, List? whiteList, VoidCallback popAction) {
  QRSYNHandler().handleScanResult(value, whiteList, popAction);
}
