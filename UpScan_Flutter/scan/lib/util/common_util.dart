import 'package:Appinfos/Appinfos.dart';
import 'package:Appinfos/AppinfosModel.dart';
import 'package:abtest/abtest.dart';
import 'package:abtest/model/abtestparam.dart';
import 'package:scan/util/qr_log.dart';

import '../config/qr_sting.dart';
import '../service/request_env.dart';

class CommonUtil {
  static ServerEnv _serverEvn = ServerEnv.SHENGCHAN;

  static Future<void> requestServerEnv() async {
    try {
      // init _appInfoModel
      final AppInfoModel appInfoModel = await AppInfoPlugin.getAppInfo();
      // init _serverEnv
      _serverEvn = _fromType(appInfoModel.env);
    } catch (error) {
      LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, dynamic>{
        'fn': 'getUserInfo exception: ${error.toString()}'
      });
    }
  }

  static ServerEnv getServerEnv() {
    return _serverEvn;
  }

  static ServerEnv _fromType(String env) {
    return env == '验收' ? ServerEnv.YANSHOU : ServerEnv.SHENGCHAN;
  }

  static T convertType<T>(dynamic param, T defaultValue) {
    final bool typeCorrect = param is T;
    final T convertResult = typeCorrect ? param : defaultValue;
    LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, String>{
      'fn':
          'convertType with type correct: $typeCorrect, param: $param, default value: $defaultValue}'
    });
    return convertResult;
  }

  /// 查询A/B测试接口，获取绑定地址，当前打开为Native还是H5
  ///
  /// 返回值：true表示H5，false表示Native
  static Future<bool> getABTestDataForBind() async {
    try {
      final ABTestParam param = ABTestParam(
        key: 'devicebindSwitch',
        cacheType: ABTestCacheType.ABTestCacheTypeCache,
        defaultValue: false,
      );
      final Map<dynamic, dynamic> abTestData =
          await ABTest.fetchABTestData(param);
      LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, String>{
        'fn': 'getABTestDataForBind: abTestData: $abTestData}'
      });
      return convertType<bool>(abTestData['result'], false);
    } catch (e) {
      LogHelper.error(
          tag: QRScanLogString.tagQRScan,
          msg: <String, String>{'fn': 'getABTestDataForBind: error: $e}'});
      return false;
    }
  }
}
