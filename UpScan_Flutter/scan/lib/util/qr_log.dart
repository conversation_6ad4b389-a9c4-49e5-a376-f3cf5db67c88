import 'package:log/log.dart';
import 'package:log/log_modle.dart';

class LogHelper{
  static const String verboseLevel = "verbose";
  static const String infoLevel = "info";
  static const String debugLevel = "debug";
  static const String warningLevel = "warning";
  static const String errorLevel = "error";
  
  // 开发调试过程中一些详细信息，不应该编译进产品中，只在开发阶段使用
  static void verbose({required String tag, required Object msg}) {
    _logToMpaas(verboseLevel, tag, msg.toString());
  }

// 一些运行时的状态信息，这些状态信息在出现问题的时候能提供帮助
  static void info({required String tag, required Object msg}) {
    _logToMpaas(infoLevel, tag, msg.toString());
  }

// 用于调试的信息，编译进产品，但可以在运行时关闭
  static void debug({required String tag, required Object msg}) {
    _logToMpaas(debugLevel, tag, msg.toString());
  }

// 警告系统出现了异常，即将出现错误
  static void warning({required String tag, required Object msg}) {
    _logToMpaas(warningLevel, tag, msg.toString());
  }

// 系统已经出现了错误
  static void error({required String tag, required Object msg}) {
    _logToMpaas(errorLevel, tag, msg.toString());
  }

  static void _logToMpaas(String level, String tag, String msg) async {
    Log.printLog(LogModle(level, tag, msg)).then((value) {
      // send to mPaas success
    }).catchError((onError) {
    });
  }
}