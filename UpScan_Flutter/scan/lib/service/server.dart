import 'dart:collection';
import 'dart:convert';

import 'package:dio/dio.dart';

import '../config/qr_sting.dart';
import '../util/qr_log.dart';

class HeaderInterceptors extends InterceptorsWrapper {
  @override
  onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    /// 超时时间设置
    options.connectTimeout = Duration(seconds: 30);
    return handler.next(options);
  }
}

class ResponseInterceptors extends InterceptorsWrapper {
  @override
  onResponse(Response response, ResponseInterceptorHandler handler) {
    return handler.next(response);
  }
}

class LogsInterceptors extends InterceptorsWrapper {
  @override
  onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    return handler.next(options);
  }

  @override
  onResponse(Response response, ResponseInterceptorHandler handler) {
    return handler.next(response);
  }

  @override
  onError(DioError err, ErrorInterceptorHandler handler) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': 'post', 'httpManager': '请求失败...'});
    return handler.next(err);
  }
}

/// http 请求封装
class HttpManager {
  Dio _dio = Dio(); // 使用默认配置
  HttpManager() {
    _dio.interceptors.add(new HeaderInterceptors());
    _dio.interceptors.add(new LogsInterceptors());
    _dio.interceptors.add(new ResponseInterceptors());
  }

  /// 统一封装 get 请求
  Future<dynamic> getData(
    String url, {
    Map<String, dynamic>? params,
    Map<String, dynamic>? header,
  }) async {
    Map<String, dynamic> _headers = new HashMap();
    if (header != null) {
      _headers.addAll(header);
    }
    Options _options = Options();
    _options.headers = header;

    Response? response;
    try {
      response =
          await _dio.get(url, queryParameters: params, options: _options);
    } on DioError catch (e) {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': 'get', 'getData': '网络请求异常: ' + e.toString()});
    }
    var data = response != null ? (response.data ?? Map()) : Map();
    if (data is String) {
      data = jsonDecode(data);
    }
    LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: {
      'fn': 'get',
      'getData': 'http response , url: $url, response: $response, data: $data'
    });

    // 兼容不同接口返回数据格式
    if (data['retCode'] == '00000' ||
        data['code'] == 200 ||
        data['status'].toString() == '0') {
      return data;
    } else {
      return Map<String, dynamic>();
    }
  }

  /// 统一封装 post 请求
  Future<dynamic> postData(
    String url, {
    Map<String, dynamic>? params,
    RequestOptions? options,
    Map<String, dynamic>? header,
    bool query = false,
  }) async {
    Options options = Options();
    options.headers = header;
    Response response;
    try {
      response = await _dio.post(url,
          data: params,
          queryParameters: query ? params : null,
          options: options);
    } on DioError catch (e) {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': 'post', 'postData': '网络请求异常: ' + e.toString()});
      throw e;
    }
    var data = response.data ?? Map<String, dynamic>();
    if (data is String) {
      data = jsonDecode(data);
    }
    // 兼容不同接口返回数据格式
    if (data['retCode'] == '00000' ||
        data['code'] == 200 ||
        data['status'].toString() == '0') {
      return data;
    } else {
      if (data['retCode'] != null) {
        return data;
      }
      return Map<String, dynamic>();
    }
  }
}

final HttpManager httpManager = new HttpManager();
