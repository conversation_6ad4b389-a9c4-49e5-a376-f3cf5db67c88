import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:scan/config/qr_sting.dart';
import 'package:scan/util/qr_log.dart';
import 'package:scan/util/qr_scan_common_util.dart';

class QRScanLoading extends StatelessWidget {


  static void show() {
    if (QRScanCommonUtil().context == null) {
      LogHelper.debug(
              tag: QRScanLogString.tagQRScan,
              msg: {'fn': 'QRScanLoading.show', 'info': 'QRScanCommonUtil().context == null'});
      return;
    }
    showDialog(
      barrierDismissible: true,
      context: QRScanCommonUtil().context!,
      builder: (ctx) => Theme(
        data: Theme.of(ctx).copyWith(dialogBackgroundColor: Colors.transparent),
        child: QRScanLoading(),
      ),
    );
  }

  static void dismiss() {
    if (QRScanCommonUtil().context == null) {
      LogHelper.debug(
              tag: QRScanLogString.tagQRScan,
              msg: {'fn': 'QRScanLoading.dismiss', 'info': 'QRScanCommonUtil().context == null'});
      return;
    }
    Navigator.pop(QRScanCommonUtil().context!);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      child: Center(
        child: Container(
          decoration: BoxDecoration(
            color: Colors.grey,
            borderRadius: BorderRadius.circular(5),
          ),
          width: 60,
          height: 60,
          alignment: Alignment.center,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              SpinKitFadingCircle(
                color: Colors.white,
                size: 46.0,
              )
            ],
          ),
        ),
      ),
    );
  }
}
