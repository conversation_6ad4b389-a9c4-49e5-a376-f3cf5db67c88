import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:log/log.dart';
import 'package:log/log_modle.dart';
import 'package:message/message.dart';
import 'package:message/msgmodel.dart';
import 'package:r_scan/r_scan.dart';
import 'package:scan/common_widget/qr_scan_toast.dart';
import 'package:scan/config/constant.dart';
import 'package:scan/config/qr_sting.dart';
import 'package:scan/util/qr_scan_common_util.dart';
import 'package:scan/util/qr_scan_string_util.dart';
import 'package:scan/util/white_code_util.dart';
import 'package:scan/util/show_help_dialog.dart';
import 'package:trace/trace.dart';
import 'package:uplustrace/uplustrace.dart';
import 'package:uppermission/requestresult.dart';
import 'package:uppermission/uppermission.dart';
import 'package:vdn/back.dart';
import 'package:vdn/vdn.dart';

import '../config/qr_scan_type.dart';
import '../handler/qr_scan_string_handler.dart';
import '../model/qr_scan_album_model.dart';
import '../model/qr_scan_camera_model.dart';
import '../model/qr_scan_query_permission_model.dart';
import '../store/qr_scan_state.dart';
import '../store/qr_scan_store.dart';
import '../util/common_util.dart';
import '../util/qr_scan_throttler.dart';
import 'qr_dialog.dart';
import '../util/qr_log.dart';

typedef QRScanResultHandler = void Function(
    List<RScanResult> result,
    List? whiteList,
    QRScanSourceType type,
    int milliseconds,
    VoidCallback popAction);

List<RScanCameraDescription>? rScanCameras;

class RSNcanCameraDialog extends StatefulWidget {
  final String description;
  final bool showPhoto;
  final bool showTakePhoto;
  final bool showTips;
  final String isAndroidFromCamera;
  final String? androidPhotoPath;

  @required
  final QRScanResultHandler scanResult;

  RSNcanCameraDialog({
    this.description = '',
    this.showPhoto = true,
    this.showTakePhoto = true,
    this.showTips = true,
    this.isAndroidFromCamera = '0',
    this.androidPhotoPath,
    required this.scanResult,
  }) {
    UplusTrace.startTrack(
        QRScanTraceCodeString.QR_SCAN_TRACE_CODE_PAGE_LOAD_TIME);
  }

  @override
  _RSNcanCameraDialogState createState() => _RSNcanCameraDialogState(
      description, showPhoto, showTakePhoto, showTips, scanResult);
}

class _RSNcanCameraDialogState extends State<RSNcanCameraDialog>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  // RScanCameraController? _controller;
  bool isFirst = true;
  String _contentDes;
  bool showPhoto;
  bool showTakePhoto;
  bool showTips;
  Timer? _timer;
  DateTime? _interDate;
  List? _whiteList;

  StreamSubscription? _resumeMessage;
  StreamSubscription? _pauseMessage;
  StreamController? _streamController;
  bool _flashMode = false;

  // 是否正在识别相册的图片
  bool _isAlbumScan = false;
  AnimationController? _animationController;

  // 扫一扫的线是否需要动画
  bool _isAnimatedLine = true;

  QRScanResultHandler? scanResult;

  // 是否展示相机设置提示
  bool _cameraTipShow = false;

  bool _leaveScanForCameraIOS = false;

  // 相机是否可用，是否已被初始化
  bool _cameraAvailable = false;

  // 是否打开相册识别，用于解决iOS端侧滑返回后扫码框不显示扫描线问题
  bool _openAlbumForIOS = false;

  _RSNcanCameraDialogState(this._contentDes, this.showPhoto, this.showTakePhoto,
      this.showTips, this.scanResult);

  Widget _gainDefaultWidgets() {
    return Container(
      alignment: Alignment.center,
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
            text: QRContentString.QR_SCAN_CONTENT,
            style: TextStyle(
              decoration: TextDecoration.none,
              fontSize: 13,
              color: Colors.white,
            ),
            children: [
              TextSpan(
                  text: QRContentString.QR_SCAN_BINDING,
                  style: TextStyle(
                    decoration: TextDecoration.none,
                    fontSize: 13,
                    color: Color(0xff2283E2),
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      showDialog(
                          context: context,
                          barrierDismissible: true,
                          builder: (context) {
                            return ShowHelpDialog();
                          });
                    }),
              WidgetSpan(
                //对齐方式
                style: TextStyle(
                  decoration: TextDecoration.none,
                ),
                alignment: PlaceholderAlignment.middle,
                //这里就是中间显示的图片了也可以是其他任意的 Widget
                child: Image.asset(
                  "images/image_show.png",
                  fit: BoxFit.cover,
                  width: 13,
                  height: 13,
                  package: 'scan',
                ),
              ),
              TextSpan(
                text: QRContentString.QR_SCAN_OTHER_DES,
                style: TextStyle(
                  decoration: TextDecoration.none,
                  fontSize: 13,
                  color: Colors.white,
                ),
              ),
            ]),
      ),
    );
  }

  void initCamera() {
    LogHelper.debug(
      tag: QRScanLogString.tagQRScan,
      msg: <String, Object>{
        'fn': 'initCamera',
        '_cameraAvailable': _cameraAvailable
      },
    );

    if (rScanCameras == null || rScanCameras!.length == 0) {
      _requestPermissionFunction('camera', allowedCallback: () {
        _reloadScanContext();
      });
    }
    _initCameraController();
  }

  Future<void> _initCameraController() async {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_initCameraController', 'rScanCameras': rScanCameras});
    if (rScanCameras != null && rScanCameras!.length > 0) {
      LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, String>{
        'fn': '_initCameraController',
        'before': '_cameraAvailable: $_cameraAvailable'
      });
      if (_cameraAvailable) {
        return;
      }

      _cameraAvailable = true;
      LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, String>{
        'fn': '_initCameraController',
        'after': '_cameraAvailable: $_cameraAvailable'
      });
      rScanController = RScanCameraController(
          rScanCameras![0], RScanCameraResolutionPreset.veryHigh)
        ..addListener(() {
          LogHelper.debug(
              tag: QRScanLogString.tagQRScan,
              msg: {'fn': '_initCameraController', 'info': 'begian scan'});
          final result = rScanController!.result;
          LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: {
            'fn': '_initCameraController',
            'result': result,
            'rScanController': rScanController,
            'isFirst': isFirst
          });
          if (result != null) {
            if (isFirst) {
              LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: {
                'fn': '_initCameraController',
                'scanResult': scanResult
              });
              //计算扫码时间差
              DateTime _now = DateTime.now();
              int _second = _interDate != null
                  ? _now.difference(_interDate!).inSeconds
                  : 1;
              if (scanResult != null) {
                scanResult!(result, _whiteList,
                    QRScanSourceType.QRScanSourceTypeCamera, _second, () {
                  _disposeEvent();
                  Vdn.close(result: {'data': result.first.message});
                  // Navigator.of(context).pop(result);
                  // _timer.cancel();
                });
              }
              //重置记录时间
              _interDate = DateTime.now();
              isFirst = false;
              //重置扫码
              resetScan();
            }
          }
        })
        ..initialize().then((_) {
          if (!mounted) {
            return;
          }
          setState(() {});
        });
      _flashMode = (await rScanController?.getFlashMode())!;
    }
  }

  void resetScan() {
    if (_timer == null) {
      _timer = Timer.periodic(Duration(milliseconds: 3000), (t) {
        isFirst = true;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    BackKeyManager.disallowSystemBack();
    BackKeyManager.registerEventChannel((event) {
      Log.printLog(LogModle('debug', '[scan]', 'scan backPress'));
      _backAction();
    });
    WidgetsBinding.instance.addObserver(this);
    //获取白名单数据
    WhiteCodeUtils.getInstance()
        .gainQrWhiteCodeFromResource(CONSTANT.zhijiaWhiteCode);
    WhiteCodeUtils.getInstance()
        .gainQrWhiteCode(CONSTANT.zhijiaWhiteCode)
        .then((value) {
      _whiteList = value;
    });
    CommonUtil.requestServerEnv();
    _interDate = DateTime.now();
    //扫码页打点
    Trace.traceEvent(
        eventId: QRScanTraceCodeString.QR_SCAN_TRACE_CODE_SCAN_PAGE);

    if (_checkGotoClothWash()) {
      //安卓相机选择的图片在此处理，处理拍照识衣
      _handleAndroidCameraData();
      return;
    }
    _streamController = StreamController<bool>();

    initCamera();
    //监听app进入前后台事件
    setResumeOrPauseEvent();

    //初始化动画，用于相册识别页面展示效果
    _animationController = AnimationController(
        duration: Duration(milliseconds: 1500), vsync: this);
    if (Platform.isAndroid) {
      RScan.getInstance().init().setCallBack(_doScanImgFromAndroid);
    }
  }

  void setResumeOrPauseEvent() {
    _resumeMessage =
        Message.listen<AppResumeMessage>((AppResumeMessage event) async {
      if (rScanController != null && _flashMode) {
        _flashMode = false;
        rScanController!.setFlashMode(_flashMode);
        _streamController?.add(_flashMode);
      }
    });

    _pauseMessage =
        Message.listen<AppPauseMessage>((AppPauseMessage event) async {
      _cameraAvailable = false;
      if (Platform.isAndroid) {
        final bool granted = await getCameraPermission();
        if (granted && rScanController != null) {
          rScanController!.dispose();
          rScanController = null;
        }
      }
    });
  }

  Future<bool> getCameraPermission() async {
    try {
      List<String> permissions = [];
      permissions.add('camera');
      List<dynamic> resultList =
          await Uppermission.queryPermission(permissions);
      final Map<String, dynamic> resultMap =
          CommonUtil.convertType<Map<dynamic, dynamic>>(
              resultList[0], <dynamic, dynamic>{}).cast<String, dynamic>();
      final QueryPermissionResult permissionResult =
          QueryPermissionResult.fromJson(resultMap);
      return '${permissionResult.granted}' == 'true';
    } catch (e) {
      return false;
    }
  }

  void _updateCameraTipShowState(bool show) {
    LogHelper.info(
        tag: QRScanLogString.tagQRScan,
        msg: 'updateCameraTipShowState, start with show = ${show}');
    if (show == _cameraTipShow || !mounted) {
      LogHelper.info(tag: QRScanLogString.tagQRScan, msg: {
        'updateCameraTipShowState err, return with show':
            '${show == _cameraTipShow} !mounted = ${!mounted}',
        'info': '${!mounted}'
      });
      return;
    }
    setState(() {
      _cameraTipShow = show;
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    LogHelper.info(
        tag: QRScanLogString.tagQRScan,
        msg: 'scan---didChangeAppLifecycleState$state.');
    switch (state) {
      case AppLifecycleState.resumed:
        onPageShow();
        break;
      case AppLifecycleState.hidden:
        onPageHide();
        break;
      case AppLifecycleState.detached:
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
        break;
    }
  }

  Future<void> onPageShow() async {
    final bool granted = await getCameraPermission();
    if (granted) {
      _updateCameraTipShowState(false);
      if (rScanController == null) {
        LogHelper.info(
            tag: QRScanLogString.tagQRScan,
            msg:
                'scan onPageShow, with controller null and _cameraAvailable $_cameraAvailable.');
        if (rScanCameras == null || rScanCameras!.isEmpty) {
          _reloadScanContext();
        }
        _initCameraController();
      }
    } else {
      _updateCameraTipShowState(true);
    }
    if (rScanController != null) {
      if (Platform.isIOS && _leaveScanForCameraIOS) {
        // 为了解决iOS端扫码打开相机后物理返回键返回后扫码不启动的问题
        _leaveScanForCameraIOS = false;
        rScanController!.startScan();
      }
      if (Platform.isIOS && _openAlbumForIOS) {
        _openAlbumForIOS = false;
        setState(() {
          _isAnimatedLine = true;
        });
      }
      if (!_isAlbumScan) {
        //没在识别相册中的图片，开始调用mPaaS
        rScanController!.startGetScanResult();
      }
      bool flashModel = (await rScanController!.getFlashMode())!;
      _flashMode = flashModel;
      _streamController?.add(_flashMode);
    }
  }

  void onPageHide() {
    if (rScanController != null) {
      rScanController!.stopGetScanResult();
      if (_flashMode != false) {
        _flashMode = false;
        rScanController!.setFlashMode(_flashMode);
        _streamController?.add(_flashMode);
      }
    }
  }

  @override
  void dispose() {
    super.dispose();
    _disposeEvent();
  }

  @override
  Widget build(BuildContext context) {
    QRScanCommonUtil().context = context;
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    //此处计算是根据裁剪框的大小设定的
    ScreenUtil.init(
        BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width,
            maxHeight: MediaQuery.of(context).size.height),
        designSize: width > height ? Size(240, 812) : Size(375, 812),
        orientation: Orientation.portrait);
    double boxWidth = ScreenUtil().screenWidth * (width > height ? 0.35 : 0.55);
    //加载中. 加载中.. 加载中... 循环显示 间隔500ms
    Tween(begin: 0.0, end: 1.0)
        .chain(CurveTween(curve: Interval(0, 1 / 3)))
        .animate(_animationController!);
    Tween(begin: 0.0, end: 1.0)
        .chain(CurveTween(curve: Interval(1 / 3, 2 / 3)))
        .animate(_animationController!);
    Tween(begin: 0.0, end: 1.0)
        .chain(CurveTween(curve: Interval(2 / 3, 1)))
        .animate(_animationController!);

    return StoreProvider<QRScanState>(
      store: qrScanStore,
      child: Scaffold(
        backgroundColor: Colors.black,
        body: Stack(
          children: <Widget>[
            _gainContainerWidget(boxWidth),
            Positioned(
              top: 90 +
                  MediaQueryData.fromView(View.of(context)).padding.top +
                  40 +
                  MediaQueryData.fromView(View.of(context)).size.width *
                      2 /
                      3 /
                      2 -
                  10,
              left:
                  MediaQueryData.fromView(View.of(context)).size.width / 2 - 50,
              child: Offstage(
                offstage: !_isAlbumScan,
                child: AnimatedBuilder(
                  animation: _animationController!,
                  builder: (BuildContext context, Widget? child) {
                    return Container(
                      height: 20,
                      constraints: BoxConstraints(minWidth: 100),
                      alignment: Alignment.center,
                      child: Text(
                        _animationController!.value <= 1 / 3
                            ? '加载中.'
                            : (_animationController!.value >= 2 / 3
                                ? '加载中...'
                                : '加载中..'),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            SingleChildScrollView(
              child: Column(
                children: <Widget>[
                  Container(
                    height:
                        MediaQueryData.fromView(View.of(context)).padding.top +
                            90,
                    child: AppBarWidget(
                      showPhoto: widget.showPhoto,
                      leftClick: _throttle(() async {
                        _backAction();
                        //扫码页取消打点
                        Trace.traceEvent(
                            eventId:
                                QRScanTraceCodeString.QR_SCAN_TRACE_CODE_BACK);
                        LogHelper.debug(
                            tag: QRScanLogString.tagQRScan,
                            msg: {'fn': 'build', 'info': '_backAction'});
                        await Future.delayed(Duration(milliseconds: 2000));
                      }),
                      // _backAction,
                      rightAction: _throttle(() async {
                        _tipsAction();
                        // _photoAction();
                        // await Future.delayed(Duration(milliseconds: 2000));
                      }),
                    ),
                  ),
                  Stack(
                    children: [
                      SizedBox(
                        height: boxWidth + 15 + 40,
                      ),
                      if (_cameraTipShow == true)
                        Container(
                          width: boxWidth,
                          height: boxWidth,
                          margin: const EdgeInsets.only(top: 40, bottom: 15),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Container(
                                margin:
                                    const EdgeInsets.only(left: 8, right: 8),
                                child: const Text(
                                    CameraPermissionConfig
                                        .QR_SCAN_CAMERA_PERMISSION_TIPS,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFFFFFFFF))),
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              GestureDetector(
                                onTap: () {
                                  // 跳转设置
                                  Vdn.goToPage(CameraPermissionConfig
                                      .QR_SCAN_CAMERA_PERMISSION_CONFIG_URL);
                                },
                                child: Container(
                                  width: 114,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF2283E2),
                                    border: Border.all(
                                        color: const Color(0xFF2283E2)),
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(18)),
                                  ),
                                  child: const Center(
                                    child: Text(
                                        CameraPermissionConfig
                                            .QR_SCAN_CAMERA_PERMISSION_OPEN,
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                            fontSize: 14,
                                            color: Color(0xFFFFFFFF))),
                                  ),
                                ),
                              )
                            ],
                          ),
                        )
                      else
                        Container(),
                    ],
                  ),
                  if (StringUtil.isBlankString(_contentDes))
                    _gainDefaultWidgets()
                  else
                    Text(
                      _contentDes,
                      style: const TextStyle(
                        fontSize: 13,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  const SizedBox(
                    height: 65,
                  ),
                  SizedBox(
                    height: 81,
                    child: AttachmentWidget(
                      flashMode: _flashMode,
                      streamController: _streamController!,
                      flashButton: _buildFlashBtn,
                      takePhotoAction: _throttle(() async {
                        _photoAction();
                        await Future.delayed(Duration(milliseconds: 2000));
                      }),
                      tipAction: _tipsAction,
                      showTipButton: widget.showTips,
                      takePhotoButton: widget.showTakePhoto,
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _gainContainerWidget(double scanBoxWidth) {
    Widget tmp;
    bool needAnimate = false;
    if (rScanCameras == null || rScanCameras!.length == 0) {
      tmp = Container(
        color: Colors.black,
        alignment: Alignment.center,
        child: Text(
          '',
          //QRContentString.QR_NO_PHOTO_PERMISSIONS,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.0,
          ),
        ),
      );
    } else if (rScanController == null ||
        !rScanController!.value.isInitialized!) {
      tmp = Container(
        color: Colors.black,
      );
    } else {
      double scaleValue = 1.3;
      try {
        double sizeRatio = ScreenUtil().uiSize.aspectRatio; //0.46
        double aspectRatio = rScanController!.value.aspectRatio; //0.5625
        String scale = (aspectRatio / sizeRatio).toStringAsFixed(1);
        scaleValue = double.parse(scale) + 0.1;
      } catch (e) {
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: {'catch': 'scalError', 'result': scaleValue});
      }
      tmp = Container(
        alignment: Alignment.center,
        child: Transform.scale(
          scale: scaleValue,
          child: AspectRatio(
            aspectRatio: rScanController!.value.aspectRatio,
            child: RScanCamera(rScanController!),
          ),
        ),
      );
      needAnimate = true;
    }
    return ScanImageView(
      boxWidth: scanBoxWidth,
      child: tmp,
      needAnimate: needAnimate && !_isAlbumScan && _isAnimatedLine,
    );
  }

  /// 函数节流
  ///
  /// [func]: 要执行的方法
  VoidCallback _throttle(
    Future Function() func,
  ) {
    bool enable = true;
    VoidCallback target = () {
      if (enable == true) {
        enable = false;
        func().then((_) {
          enable = true;
        });
      }
    };
    return target;
  }

  void _backAction() {
    _disposeEvent();
    Vdn.close();
  }

  void _disposeEvent() {
    _cameraAvailable = false;
    if (Platform.isAndroid) {
      if (!mounted) {
        return;
      }
      // 处理Android 扫码页面内存泄漏问题; ios 端在native侧处理
      rScanController?.removeListener(() {});
      rScanController?.dispose();
      _timer?.cancel();
      _resumeMessage?.cancel();
      _pauseMessage?.cancel();
      _streamController?.close();
      _animationController?.dispose();
      if (Platform.isAndroid) {
        RScan.getInstance().destroy();
      }
      WidgetsBinding.instance.removeObserver(this);
      QRScanThrottle().dispose();
    }
  }

  void _photoAction() {
    //相机按钮点击打点
    Trace.traceEvent(eventId: QRScanTraceCodeString.QR_SCAN_TRACE_CODE_PHOTO);
    setState(() {
      _isAnimatedLine = false;
    });
    _openAlbum();
  }

  void _openAlbum() {
    _openAlbumForIOS = true;
    const int maxSelected = 1;
    const bool isFromScan = true;
    const bool originalImage = true;
    Vdn.goToPage('flutter://photocheck', params: <String, String>{
      'max': '$maxSelected',
      'isFromScan': '$isFromScan',
      'showOriginalBtn': '$originalImage'
    }).then((Map mapValue) async {
      Log.printLog(
          LogModle('debug', '[scan]', 'scan-openPhotoPicker $mapValue'));
      _openAlbumForIOS = false;
      if (mapValue['data'] is List?) {
        List? value = mapValue['data'] as List?;
        if (value != null && value.length > 0) {
          if (value[0] is! Map) {
            setState(() {
              _isAnimatedLine = true;
            });
            return;
          }
          final Map<String, dynamic> pathMap =
              CommonUtil.convertType<Map<dynamic, dynamic>>(
                  value[0], <dynamic, dynamic>{}).cast<String, dynamic>();
          final QrAlbumResultData albumResultData =
              QrAlbumResultData.fromJson(pathMap);
          final String? path = albumResultData.path;
          if (!StringUtil.isBlankString(path)) {
            _animationController!.repeat();
            setState(() {
              _isAlbumScan = true;
            });
            if (Platform.isIOS) {
              final result = await RScan.scanImagePath(path!);
              _animationController!.stop();
              setState(() {
                _isAlbumScan = false;
              });
              if (result == null) {
                setState(() {
                  _isAnimatedLine = true;
                });
                QRScanToast()
                    .showErrorText(QRScanErrorString.QR_NO_SCAN_RESULT);
              } else {
                _closeRotue(result);
              }
              if (rScanController != null) {
                rScanController!.startGetScanResult();
              }
            } else if (Platform.isAndroid) {
              RScan.getInstance().scanImagePathFromAndroid(path!);
            }
          } else {
            setState(() {
              _isAnimatedLine = true;
            });
          }
        } else {
          setState(() {
            _isAnimatedLine = true;
          });
        }
      }
    });
  }

  /// 针对安卓相册图片扫码回调函数
  void _doScanImgFromAndroid(List<RScanResult>? result) {
    if (_animationController != null) {
      _animationController!.stop();
    }
    setState(() {
      _isAlbumScan = false;
    });
    if (result == null || result.isEmpty) {
      setState(() {
        _isAnimatedLine = true;
      });
      QRScanToast().showErrorText(QRScanErrorString.QR_NO_SCAN_RESULT);
    } else {
      _closeRotue(result);
    }
    if (rScanController != null) {
      rScanController!.startGetScanResult();
    }
  }

  // void _takePhotoAction() async {
  //   LoginStatus status = await User.getLoginStatus();
  //   if (status.isLogin != true) {
  //     QRScanToast().showErrorText(QRScanErrorString.QR_NOT_LOGIN_PROMPT);
  //     return;
  //   }
  //   if (Platform.isIOS) {
  //     _openCameraForIOS();
  //   } else {
  //     _openCameraForAndroid();
  //   }
  // }

  void _openCameraForAndroid() {
    const bool isFromScan = true;
    _disposeEvent();
    Vdn.goToPage('flutter://cameraentry',
            params: <String, dynamic>{'isFromScan': '$isFromScan'})
        .then((value) => null);
    Vdn.close();
  }

  // 检查安卓端是否进入拍照识衣
  bool _checkGotoClothWash() {
    bool isAndroidFromCamera = false;
    if (!StringUtil.isBlankString(widget.isAndroidFromCamera)) {
      isAndroidFromCamera =
          int.parse(widget.isAndroidFromCamera) == 1 ? true : false;
    }

    if (Platform.isIOS || isAndroidFromCamera == false) {
      return false;
    }

    if (StringUtil.isBlankString(widget.androidPhotoPath)) {
      QRScanToast().showErrorText(QRScanErrorString.QR_NOT_GAIN_PHOTO);
      return false;
    }

    return true;
  }

  void _handleAndroidCameraData() {
    _disposeEvent();
    Vdn.close();
    // _backAction();
    String clothWashUrl = QRString.QR_SCAN_CLOUD_CLOTH_WASH_PAGE_URL_SHENGCHAN
        .replaceAll('%@', widget.androidPhotoPath ?? '');
    Vdn.goToPage(clothWashUrl).then((result) {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': '_handleAndroidCameraData', 'result': result});
    }).catchError((e) {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': '_handleAndroidCameraData', 'catchError': e});
    });
  }

  void _openCameraForIOS() {
    rScanController?.stopScan();
    const bool isFromScan = true;
    _leaveScanForCameraIOS = true;
    Vdn.goToPage('flutter://cameraentry',
        params: <String, String>{'isFromScan': '$isFromScan'}).then((value) {
      rScanController?.startScan();
      String pathStr = _gainCameraPath(value);
      LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: {
        'fn': '_openCameraForIOS',
        'pathStr': pathStr,
        'onResult': value.toString()
      });
      if (StringUtil.isBlankString(pathStr)) {
        QRScanToast().showErrorText(QRScanErrorString.QR_NOT_GAIN_PHOTO);
      } else {
        rScanController?.startScan();
        _backAction();
        String clothWashUrl = QRString
            .QR_SCAN_CLOUD_CLOTH_WASH_PAGE_URL_SHENGCHAN
            .replaceAll('%@', pathStr);
        Vdn.goToPage(clothWashUrl).then((result) {
          LogHelper.debug(
              tag: QRScanLogString.tagQRScan,
              msg: {'fn': '_openCameraForIOS', 'result': result});
        }).catchError((e) {
          LogHelper.debug(
              tag: QRScanLogString.tagQRScan,
              msg: {'fn': '_openCameraForIOS', 'catchError': e});
        });
      }
    });
  }

  String _gainCameraPath(Map<dynamic, dynamic>? result) {
    if (result == null || result.isEmpty) {
      return '';
    }
    final Map<String, dynamic> map =
        CommonUtil.convertType<Map<dynamic, dynamic>>(
            result, <dynamic, dynamic>{}).cast<String, dynamic>();
    final QrCameraResult cameraResult = QrCameraResult.fromJson(map);
    final QrCameraResultData? retData = cameraResult.retData;
    if (retData == null || retData?.data == null) {
      return '';
    }
    return retData!.data!;
  }

  Future<void> _requestPermissionFunction(String permissionName,
      {VoidCallback? allowedCallback,
      VoidCallback? cancelCallback,
      bool isImmediate = false}) async {
    try {
      final RequestResult result = await Uppermission.requestPermission(
          [permissionName],
          immediate: isImmediate);
      if (result.isAllowed == false) {
        _updateCameraTipShowState(true);
        if (cancelCallback != null) {
          cancelCallback();
        }
      } else {
        _updateCameraTipShowState(false);
        if (allowedCallback != null) {
          allowedCallback();
        }
      }
    } catch (e) {
      _updateCameraTipShowState(false);
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': '_requestPermissionFunction', 'catchError': e});
    }
  }

  void _reloadScanContext() async {
    rScanCameras = await availableRScanCameras();
    _initCameraController();
    //此处这么处理是为了等_controller 初始化完成
    Timer.periodic(Duration(milliseconds: 200), (t) {
      if (rScanController != null) {
        rScanController!.startGetScanResult();
      }
      t.cancel();
    });
  }

  void _closeRotue(List<RScanResult> result) async {
    ///延时0.3秒，为了让当前vdn能够退出
    await Future.delayed(Duration(milliseconds: 500), () {
      //计算扫码时间差
      DateTime _now = DateTime.now();
      int _second = _now.difference(_interDate!).inSeconds;
      if (scanResult != null) {
        scanResult!(
            result, _whiteList, QRScanSourceType.QRScanSourceTypeAlbum, _second,
            () {
          _disposeEvent();
          Vdn.close(result: {'data': result.first.message});
        });
      }
      //重置记录时间
      _interDate = DateTime.now();
    });
    setState(() {
      _isAnimatedLine = true;
    });
  }

  void _tipsAction() {
    Vdn.goToPage(QRString.QR_SCAN_HELP_PAGE_STRING).then((value) {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': '_tipsAction', 'value': value});
    }).catchError((e) {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': '_tipsAction', 'catchError': e});
    });
    //通用扫码示例
    // openGeneralScan(
    //   isShowAlbum: true,
    //   // scanError: '不符合规则',
    //   // scanRules: 'uplus://joinFamily/',
    //   result: (value) {
    //   print('---openGeneralScan---hahah:$value');
    // },);
    // Vdn.goToPage(QRString.QR_SCAN_GENERAL_STRING,params: {
    //   "scanTitle": '通用扫码--vdn传入',
    //   // 'scanError': '通用扫码失败',
    //   // 'scanRules' : 'uplus://joinFamily/',
    //   "isShowAlbum": 'true',
    //   // "scanContent": 'scanContent',
    //   // "btn1_Title": 'btn1_Title',
    //   // "btn1_Link" : QRString.QR_SCAN_HELP_PAGE_STRING,
    //   "btn2_Title" : 'btn2_Title',
    //   "btn2_Link" : QRString.QR_SCAN_HELP_PAGE_STRING
    // }).then((value){
    //   print('-----general san reslut:$value');
    // });
  }

  // void _generalScanRegist(){
  //   FlutterBoost.singleton.registerPageBuilders(<String, PageBuilder>{
  //     QRString.QR_SCAN_GENERAL_STRING:
  //         (String pageName, Map<String, dynamic> params, String _) =>
  //           gotoQrGeneralScan(
  //             scanTitle: params['scanTitle'] != null ? params['scanTitle'] : '通用扫码',
  //             scanContent: params['scanContent'] != null ? params['scanContent'] : null,
  //             isShowAlbum: params['isShowAlbum'] != null
  //               ? (params['isShowAlbum'] == 'true' ? true : false)
  //               : false,
  //             scanRules: params['scanRules'] != null ? params['scanRules'] : null,
  //             scanError: params['scanError'] != null ? params['scanError'] : null,
  //             btn1_Title: params['btn1_Title'] != null ? params['btn1_Title'] : null,
  //             btn1_Link: params['btn1_Link'] != null ? params['btn1_Link'] : null,
  //             btn2_Title: params['btn2_Title'] != null ? params['btn2_Title'] : null,
  //             btn2_Link: params['btn2_Link'] != null ? params['btn2_Link'] : null,
  //           )
  //   });
  // }

  Widget _buildFlashBtn(BuildContext context, AsyncSnapshot<bool> snapshot) {
    return snapshot.hasData
        ? IconButton(
            icon: snapshot.data!
                ? Image.asset(
                    'images/torch_open.png',
                    package: 'scan',
                  )
                : Image.asset(
                    'images/torch.png',
                    package: 'scan',
                  ),
            color: Colors.white,
            iconSize: 46,
            onPressed: flashButtonPressed,
          )
        : Container();
  }

  void flashButtonPressed() {
    _requestPermissionFunction('camera', isImmediate: true,
        allowedCallback: () {
      if (rScanController != null) {
        _flashMode = !_flashMode;
        rScanController!.setFlashMode(_flashMode);
        _streamController?.add(_flashMode);
      } else {
        // 如果权限已同意，且摄像头未初始化，此时初始化摄像头
        if (Platform.isAndroid) {
          _reloadScanContext();
        }
      }
    });
  }
}

class AppBarWidget extends StatelessWidget {
  final bool showPhoto;
  final VoidCallback? leftClick;
  final VoidCallback? rightAction;

  AppBarWidget({this.leftClick, this.rightAction, this.showPhoto = true});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          BackButton(
            onPressed: leftClick,
            color: Colors.white,
          ),
          const Text(
            QRContentString.QR_SCAN,
            style: TextStyle(
              fontSize: 17,
              color: Colors.white,
            ),
          ),
          IconButton(
            icon: Image.asset(
              'images/tips.png',
              package: 'scan',
              width: 18,
              height: 18,
            ),
            color: Colors.white,
            iconSize: 18,
            onPressed: rightAction,
          ),
        ],
      ),
    );
  }
}

class AttachmentWidget extends StatefulWidget {
  final VoidCallback? takePhotoAction;
  // final VoidCallback? torchAction;
  final VoidCallback? tipAction;
  final bool showTipButton;
  final bool takePhotoButton;
  final StreamController streamController;
  @required
  final bool flashMode;
  @required
  final Widget Function(BuildContext, AsyncSnapshot<bool>) flashButton;

  AttachmentWidget(
      {required this.flashMode,
      required this.flashButton,
      this.takePhotoAction,
      // this.torchAction,
      this.tipAction,
      required this.streamController,
      this.showTipButton = true,
      this.takePhotoButton = true});

  _AttachmentWidgetState createState() => _AttachmentWidgetState();
}

class _AttachmentWidgetState extends State<AttachmentWidget> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: <Widget>[
        Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            StreamBuilder<bool>(
              initialData: widget.flashMode,
              stream: widget.streamController.stream as Stream<bool>,
              builder: widget.flashButton,
            ),
            const Text(
              QRContentString.QR_SCAN_TORCH,
              style: TextStyle(
                fontSize: 13,
                color: Colors.white,
              ),
            ),
          ],
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            IconButton(
                alignment: Alignment.center,
                icon: Image.asset(
                  'images/new_album.png',
                  package: 'scan',
                  width: 36,
                  height: 36,
                ),
                color: Colors.white,
                iconSize: 36,
                onPressed: widget.takePhotoAction),
            const Text(
              '相册',
              style: TextStyle(
                fontSize: 13,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
