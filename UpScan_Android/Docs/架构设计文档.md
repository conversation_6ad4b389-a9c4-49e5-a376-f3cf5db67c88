# UpScan Android 架构设计文档

## 1. 项目概述

UpScan Android 是基于QBarSDK实现的原生Android扫码库，严格按照Flutter中原有逻辑处理扫码结果。项目采用分层架构设计，确保代码的可维护性和扩展性。

## 2. 架构设计

### 2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    扫码业务层 (Business Layer)                │
├─────────────────────────────────────────────────────────────┤
│  扫码页面类                    │  Handler类                   │
│  ┌─────────────────────────┐  │  ┌─────────────────────────┐ │
│  │ QRCameraScanActivity    │  │  │ QRScanStringHandler     │ │
│  │ QRGeneralScanActivity   │  │  │ QRScanGotoPageHandler   │ │
│  │ QRSynCameraScanActivity │  │  │ QRGeneralScanHandler    │ │
│  │ ...                     │  │  │ QRSYNHandler            │ │
│  └─────────────────────────┘  │  └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  基础扫码能力层 (Core Layer)                  │
├─────────────────────────────────────────────────────────────┤
│ 扫码页面类    │ 腾讯扫码SDK    │ 共有逻辑部分   │ Handler基类  │
│ ┌───────────┐ │ ┌───────────┐ │ ┌───────────┐ │ ┌─────────┐ │
│ │BaseScan   │ │ │QBarSDK    │ │ │Config     │ │ │BaseHand │ │
│ │Activity   │ │ │Wrapper    │ │ │Model      │ │ │ler      │ │
│ │           │ │ │           │ │ │Util       │ │ │         │ │
│ │           │ │ │           │ │ │Service    │ │ │         │ │
│ └───────────┘ │ └───────────┘ │ └───────────┘ │ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 目录结构详解

```
UpScan_Android/
├── Docs/                                    # 项目文档
│   ├── 架构设计文档.md
│   └── API文档.md
├── src/main/
│   ├── java/com/uplus/upscan/
│   │   ├── business/                        # 扫码业务层
│   │   │   ├── pages/                       # 业务扫码页面
│   │   │   │   ├── camera/                  # 相机扫码页面
│   │   │   │   └── general/                 # 通用扫码页面
│   │   │   └── handlers/                    # 业务Handler
│   │   │       ├── string/                  # 字符串处理Handler
│   │   │       └── gotopage/               # 页面跳转Handler
│   │   └── core/                           # 基础扫码能力层
│   │       ├── pages/                      # 基础扫码页面
│   │       ├── qbarsdk/                    # QBarSDK集成
│   │       │   └── wrapper/                # SDK封装类
│   │       ├── common/                     # 公共组件
│   │       │   ├── config/                 # 配置类
│   │       │   ├── model/                  # 数据模型
│   │       │   ├── util/                   # 工具类
│   │       │   └── service/                # 服务类
│   │       └── handlers/                   # Handler基类
│   └── res/                                # 资源文件
│       ├── layout/                         # 布局文件
│       ├── values/                         # 值资源
│       └── drawable/                       # 图片资源
└── README.md
```

## 3. 模块职责

### 3.1 扫码业务层 (Business Layer)

#### 3.1.1 扫码页面类 (business/pages)
- **camera/**: 相机扫码相关页面
  - 对应Flutter的`qr_camera_scan.dart`
  - 处理相机权限、扫码界面交互
  
- **general/**: 通用扫码页面
  - 对应Flutter的`qr_general_scan.dart`
  - 提供通用扫码功能

#### 3.1.2 Handler类 (business/handlers)
- **string/**: 字符串处理Handler
  - 对应Flutter的`qr_scan_string_handler.dart`
  - 解析扫码结果字符串，判断扫码类型
  
- **gotopage/**: 页面跳转Handler
  - 对应Flutter的`qr_scan_goto_page_handler.dart`
  - 根据扫码结果进行页面跳转

### 3.2 基础扫码能力层 (Core Layer)

#### 3.2.1 扫码页面类 (core/pages)
- 提供基础的扫码Activity基类
- 封装通用的扫码界面逻辑

#### 3.2.2 腾讯扫码SDK (core/qbarsdk)
- **wrapper/**: QBarSDK的封装类
- 提供统一的扫码接口
- 处理SDK的初始化和配置

#### 3.2.3 共有逻辑部分公共类 (core/common)
- **config/**: 配置类
  - 对应Flutter的`config/`目录
  - 扫码类型、常量定义等
  
- **model/**: 数据模型
  - 对应Flutter的`model/`目录
  - 扫码结果、响应模型等
  
- **util/**: 工具类
  - 对应Flutter的`util/`目录
  - 通用工具方法
  
- **service/**: 服务类
  - 对应Flutter的`service/`目录
  - 网络请求、HTTP服务等

#### 3.2.4 Handler基类 (core/handlers)
- 提供Handler的基础抽象类
- 定义通用的处理接口

## 4. 设计原则

1. **分层架构**: 业务层和核心层分离，确保职责清晰
2. **依赖倒置**: 业务层依赖核心层的抽象接口
3. **单一职责**: 每个类只负责一个特定功能
4. **开闭原则**: 对扩展开放，对修改关闭
5. **Flutter对齐**: 严格按照Flutter逻辑实现，确保结果处理一致性

## 5. 下一步计划

1. 创建基础类和接口定义
2. 实现QBarSDK的封装
3. 实现核心扫码逻辑
4. 实现业务Handler
5. 创建扫码页面
6. 编写单元测试
