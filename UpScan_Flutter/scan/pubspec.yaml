name: scan
description: A new Flutter package project.
version: 0.0.17
flutterVersion: 3
author: <EMAIL>
homepage: http://**************:8083
publish_to: http://**************:8083

environment:
  sdk: ">=3.2.3 <4.0.0"
  flutter: ">=3.16.5"

dependencies:
  flutter:
    sdk: flutter
  dio: ">=4.0.0"
  vdn:
    hosted:
      name: vdn
      url: http://**************:8083
    version: ">=0.0.14"

  Appinfos:
    hosted:
      name: Appinfos
      url: http://**************:8083
    version: ">=1.0.1"

  message:
    hosted:
      name: message
      url: http://**************:8083
    version: ">=0.0.13"

  user:
    hosted:
      name: user
      url: http://**************:8083
    version: ">=0.1.2"

  family:
    hosted:
      name: family
      url: http://**************:8083
    version: ">=0.0.3"

  storage:
    hosted:
      name: storage
      url: http://**************:8083
    version: ">=0.0.4"

  photo:
    hosted:
      name: photo
      url: http://**************:8083
    version: ">=0.0.30"

  uppermission:
    version: ">=0.0.1"
    hosted:
      name: uppermission
      url: http://**************:8083

  camera_camera:
    hosted:
      name: camera_camera
      url: http://**************:8083
    version: ">=0.0.2"

  r_scan:
    hosted:
      name: r_scan
      url: http://**************:8083
    version: ">=0.0.10"

  trace:
    hosted:
      name: trace
      url: http://**************:8083
    version: ">=0.0.12"

  uplustrace:
    hosted:
      name: uplustrace
      url: http://**************:8083
    version: ">=1.0.0"

  log:
    hosted:
      name: log
      url: http://**************:8083
    version: ">=0.0.1"

  resource:
    hosted:
      name: resource
      url: http://**************:8083
    version: ">=0.0.1"

  abtest:
    hosted:
      name: abtest
      url: http://**************:8083
    version: ">=0.0.1"

  flutter_screenutil: 5.0.0+2
  #toast弹框
  fluttertoast: ">=8.0.7"
  #程序运行loading
  flutter_spinkit: ">=5.0.0"
  convert: ">=3.0.0"
  #拨打电话
  url_launcher: ^6.0.7
  redux: 5.0.0
  flutter_redux: 0.8.2

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # To add assets to your package, add an assets section, like this:
  uses-material-design: true
  assets:
    - preresource/
    - images/
    - images/torch_open.png
    - images/image_show.png
    - images/bind_image.png
    - images/bind_cancel.png
    - images/cancel.png
    - images/upscan_torch_default.png
    - images/upscan_torch_selected.png
    - images/scan_tips.png
    - images/photo.png
    - images/torch.png
    - images/album.png
    - images/2.0x/torch_open.png
    - images/2.0x/scan_tips.png
    - images/2.0x/photo.png
    - images/2.0x/torch.png
    - images/2.0x/album.png
    - images/3.0x/torch_open.png
    - images/3.0x/scan_tips.png
    - images/3.0x/photo.png
    - images/3.0x/torch.png
    - images/3.0x/album.png
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
