// 该方法用于显示底部弹窗
import 'dart:io';

import 'package:flutter/material.dart';

import '../util/qr_scan_throttler.dart';

enum InviteType {
  invite_family,
  invalid_code,
}

Future<void> showModalBottomSheetWidget(
  BuildContext context, {
  required String title,
  required InviteType inviteType,
  required Widget child,
  VoidCallback? onCancel,
  VoidCallback? onConfirm,
}) {
  return showModalBottomSheet<void>(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    builder: (BuildContext context) {
      // 获取底部安全区域高度
      final double bottomPadding = MediaQuery.of(context).padding.bottom;
      final double marginBottom = bottomPadding == 0 ? 12 : bottomPadding;

      return DefaultTextStyle(
        style: TextStyle(
          color: const Color(0xFF111111),
          fontFamilyFallback: Platform.isIOS ? <String>['PingFang SC'] : null,
        ),
        child: Container(
          margin: EdgeInsets.only(left: 12, right: 12, bottom: marginBottom),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(32),
            color: const Color(0xfff5f5f5),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              const Padding(
                padding: EdgeInsets.only(top: 8),
                child: _DragHandlerWidget(),
              ),
              Container(
                height: 44,
                alignment: Alignment.topCenter,
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    color: Color(0xff111111),
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              // 内容部分
              Container(
                child: child,
              ),
              // 底部按钮
              _BottomButtonWidget(
                inviteType: inviteType,
                onCancel: onCancel,
                onConfirm: onConfirm,
              ),
            ],
          ),
        ),
      );
    },
  );
}

class _DragHandlerWidget extends StatelessWidget {
  const _DragHandlerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 4,
      width: 32,
      decoration: BoxDecoration(
        color: const Color(0xFFE5E5E5),
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }
}

/// 底部按钮
class _BottomButtonWidget extends StatelessWidget {
  const _BottomButtonWidget({
    super.key,
    required this.inviteType,
    this.onCancel,
    this.onConfirm,
  });

  final InviteType inviteType;
  final VoidCallback? onCancel;
  final VoidCallback? onConfirm;
  @override
  Widget build(BuildContext context) {
    // 如果是异常情况
    if (inviteType == InviteType.invalid_code) {
      return GestureDetector(
        onTap: onCancel,
        child: Container(
          alignment: Alignment.center,
          height: 44,
          margin: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: const Color(0xFF0081FF),
          ),
          child: const Text(
            '我知道了',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      );
    }
    return Row(
      children: <Widget>[
        Expanded(
          child: GestureDetector(
            onTap: onCancel,
            child: Container(
              alignment: Alignment.center,
              height: 44,
              margin: const EdgeInsets.only(right: 8, bottom: 16, top: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: Colors.white,
              ),
              child: const Text(
                '忽略',
                style: TextStyle(
                  color: Color(0xff111111),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () => QRScanThrottle().throttler.run(() {
              if (onConfirm == null) {
                return;
              }
              onConfirm!();
            }),
            child: Container(
              alignment: Alignment.center,
              height: 44,
              margin: const EdgeInsets.only(left: 8, bottom: 16, top: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: const Color(0xFF0081FF),
              ),
              child: const Text(
                '加入',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
