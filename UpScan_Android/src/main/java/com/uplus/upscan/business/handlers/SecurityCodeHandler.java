package com.uplus.upscan.business.handlers;

import android.text.TextUtils;
import android.util.Log;

import com.uplus.upscan.core.handlers.UpScanBaseHandler;
import com.uplus.upscan.core.handlers.UpScanResultSource;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 安全码处理Handler
 * 处理以"MIGRATE_QR$"开头的安全迁移码
 * 对应Flutter中的_parseSecurity方法
 */
public class SecurityCodeHandler extends UpScanBaseHandler {
    
    private static final String TAG = "SecurityCodeHandler";
    
    // 安全码标识，对应Flutter中的QR_SCAN_SECURITY_CODE_MARK
    private static final String SECURITY_CODE_MARK = "MIGRATE_QR$";
    
    // API URL，对应Flutter中的ApiUrl.QR_SCAN_SECURITY_URL
    private static final String SECURITY_API_URL = "/api-gw/wisdomdevice/device/task/anonymous/query";
    
    // 错误信息常量，对应Flutter中的QRScanErrorString
    private static final String ERROR_NO_NETWORK = "网络不可用";
    private static final String ERROR_NOT_LOGIN = "用户未登录";
    private static final String ERROR_NO_PARSE = "不支持该二维码/条形码";
    private static final String ERROR_TASK_INFO = "迁移任务不存在";
    private static final String ERROR_TASK_OVER = "超过用户绑定设备数量限制，请联系客服处理";
    
    // 请求返回码常量，对应Flutter中的QRScanRequestString
    private static final String REQUEST_SUCCESS = "00000";
    private static final String REQUEST_TASK_NO_EXIST = "1080012";
    private static final String REQUEST_OVER_MAX = "1080020";
    
    // 任务状态常量，对应Flutter中的SecurityTaskState
    private static final int SECURITY_NORMAL = 0;  // 待交割
    private static final int SECURITY_DONE = 1;    // 已完成
    private static final int SECURITY_CANCEL = 2;  // 已取消
    private static final int SECURITY_PAUSE = 3;   // 已锁定
    
    @Override
    public boolean canHandle(String code) {
        // 判断是否以"MIGRATE_QR$"开头
        return !TextUtils.isEmpty(code) && code.startsWith(SECURITY_CODE_MARK);
    }
    
    @Override
    public void doHandle(String code, UpScanResultSource source) {
        Log.d(TAG, "开始处理安全码: " + code);
        
        // 暂停扫码
        pauseScan();
        
        // 1. 检查网络状态
        boolean hasNetwork = checkNetworkStatus();
        if (!hasNetwork) {
            Log.e(TAG, "网络不可用");
            showErrorToast(ERROR_NO_NETWORK);
            resumeScan();
            return;
        }
        
        // 2. 检查登录状态
        boolean isLoggedIn = checkLoginStatus();
        if (!isLoggedIn) {
            Log.e(TAG, "用户未登录");
            showErrorToast(ERROR_NOT_LOGIN);
            resumeScan();
            return;
        }
        
        // 3. 解析任务ID
        String taskId = parseTaskId(code);
        if (TextUtils.isEmpty(taskId)) {
            Log.e(TAG, "任务ID解析失败");
            showErrorToast(ERROR_NO_PARSE);
            resumeScan();
            return;
        }
        
        Log.d(TAG, "解析到任务ID: " + taskId);
        
        // 4. 发起网络请求查询任务状态
        requestSecurityTaskInfo(taskId, code);
    }
    
    /**
     * 解析任务ID
     * 对应Flutter中的任务ID解析逻辑
     */
    private String parseTaskId(String code) {
        if (TextUtils.isEmpty(code)) {
            return null;
        }
        
        // 去掉前缀"MIGRATE_QR$"
        String firstSubString = code.substring(SECURITY_CODE_MARK.length());
        
        // 查找$符号位置
        int dollarIndex = firstSubString.indexOf("$");
        if (dollarIndex == -1) {
            return null;
        }
        
        // 提取任务ID
        return firstSubString.substring(0, dollarIndex);
    }
    
    /**
     * 请求安全任务信息
     * 对应Flutter中的网络请求逻辑
     */
    private void requestSecurityTaskInfo(String taskId, String originalCode) {
        // 构建请求参数
        JSONObject params = new JSONObject();
        try {
            params.put("taskId", taskId);
        } catch (JSONException e) {
            Log.e(TAG, "构建请求参数失败", e);
            showErrorToast(ERROR_TASK_INFO);
            resumeScan();
            return;
        }
        
        Log.d(TAG, "发起网络请求，参数: " + params.toString());
        
        // 模拟网络请求（实际项目中需要使用真实的网络请求）
        simulateNetworkRequest(params, originalCode, taskId);
    }
    
    /**
     * 模拟网络请求
     * 实际项目中需要替换为真实的网络请求实现
     */
    private void simulateNetworkRequest(JSONObject params, String originalCode, String taskId) {
        // 这里用伪代码模拟网络请求
        boolean requestSuccess = true; // 模拟请求成功
        
        if (!requestSuccess) {
            Log.e(TAG, "网络请求失败");
            showErrorToast(ERROR_TASK_INFO);
            resumeScan();
            return;
        }
        
        // 模拟响应数据
        JSONObject response = createMockResponse(taskId);
        handleSecurityResponse(response, originalCode, taskId);
    }
    
    /**
     * 创建模拟响应数据
     */
    private JSONObject createMockResponse(String taskId) {
        JSONObject response = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            response.put("retCode", REQUEST_SUCCESS);
            data.put("taskState", SECURITY_NORMAL);
            data.put("taskId", taskId);
            response.put("data", data);
        } catch (JSONException e) {
            Log.e(TAG, "创建模拟响应失败", e);
        }
        return response;
    }
    
    /**
     * 处理安全任务响应
     * 对应Flutter中的响应处理逻辑
     */
    private void handleSecurityResponse(JSONObject response, String originalCode, String taskId) {
        try {
            String retCode = response.optString("retCode");
            
            if (TextUtils.isEmpty(retCode)) {
                showErrorToast(ERROR_TASK_INFO);
                resumeScan();
                return;
            }
            
            if (REQUEST_SUCCESS.equals(retCode)) {
                JSONObject data = response.optJSONObject("data");
                if (data == null) {
                    showErrorToast(ERROR_TASK_INFO);
                    resumeScan();
                    return;
                }
                
                int taskState = data.optInt("taskState", -1);
                if (taskState == SECURITY_NORMAL || taskState == SECURITY_PAUSE) {
                    // 任务状态正常，添加taskId到数据中
                    data.put("taskId", taskId);
                    
                    // 跳转到安全迁移页面
                    navigateToSecurityPage(SECURITY_API_URL, data.toString());
                } else {
                    showErrorToast(ERROR_TASK_INFO);
                    resumeScan();
                }
            } else if (REQUEST_TASK_NO_EXIST.equals(retCode)) {
                showErrorToast(ERROR_TASK_INFO);
                resumeScan();
            } else if (REQUEST_OVER_MAX.equals(retCode)) {
                showErrorToast(ERROR_TASK_OVER);
                resumeScan();
            } else {
                showErrorToast(ERROR_TASK_INFO);
                resumeScan();
            }
            
        } catch (JSONException e) {
            Log.e(TAG, "处理响应数据异常", e);
            showErrorToast(ERROR_TASK_INFO);
            resumeScan();
        }
    }
    
    /**
     * 跳转到安全迁移页面
     * 对应Flutter中的页面跳转逻辑
     */
    private void navigateToSecurityPage(String pageUrl, String data) {
        Log.d(TAG, "跳转到安全迁移页面: " + pageUrl + ", 数据: " + data);
        
        // 这里实现页面跳转逻辑
        // 实际项目中需要根据具体的页面跳转方式实现
        
        // 处理完成后恢复扫码
        resumeScan();
    }
    
    /**
     * 检查网络状态
     * 实际项目中需要实现真实的网络检查
     */
    private boolean checkNetworkStatus() {
        // 伪代码：检查网络状态
        return true; // 模拟网络可用
    }
    
    /**
     * 检查登录状态
     * 实际项目中需要实现真实的登录状态检查
     */
    private boolean checkLoginStatus() {
        // 伪代码：检查登录状态
        return true; // 模拟已登录
    }
    
    /**
     * 显示错误提示
     * 实际项目中需要实现真实的Toast显示
     */
    private void showErrorToast(String message) {
        Log.e(TAG, "显示错误提示: " + message);
        // 这里实现Toast显示逻辑
    }
}
