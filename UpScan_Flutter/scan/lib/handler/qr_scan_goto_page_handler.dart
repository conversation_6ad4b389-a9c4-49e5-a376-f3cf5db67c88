import 'dart:convert';

import 'package:Appinfos/Appinfos.dart';
import 'package:Appinfos/AppinfosModel.dart';
import 'package:scan/config/qr_scan_type.dart';
import 'package:scan/model/qr_scan_response_model.dart';
import 'package:scan/util/qr_scan_internet_util.dart';
import 'package:scan/config/qr_sting.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:vdn/vdn.dart';
import 'package:flutter/material.dart';
import 'package:user/user.dart';
import 'package:family/family.dart';
import 'package:scan/common_widget/qr_scan_toast.dart';
import 'package:scan/util/qr_log.dart';
import '../common_widget/qr_scan_join_family_loading.dart';
import '../config/qr_api_url.dart';
import '../service/request_env.dart';
import '../service/server.dart';
import '../util/device_sign.dart';
import 'qr_scan_string_handler.dart';

class QRScanGotoPageHandler {
  void classificationGotoPage(String pageUrl, String originalStr,
      QRScanType type, VoidCallback popAction) {
    LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, dynamic>{
      'fn': 'classificationGotoPage',
      'pageUrl': pageUrl,
      'originalStr': originalStr,
      'type': type,
      'popAction': popAction
    });
    if (type == QRScanType.QRScanTypeBuyBinding) {
      _gotoBindingPage(pageUrl, originalStr, popAction);
    } else if (type == QRScanType.QRScanTypeAuthorizeLogin) {
      Map<String, String>? param = <String, String>{
        "code_type": 'QROauth',
        "code_content": originalStr,
      };
      popAction();
      _gotoPage(pageUrl, param);
    } else if (type == QRScanType.QRScanTypeBuyJump) {
      popAction();
      _gotoPage(pageUrl, null);
    } else if (type == QRScanType.QRScanTypeJoinFamily) {
      _joinFamilySucessWithFamilyId(pageUrl).then((_) {
        rScanController?.startGetScanResult();
        JoinFamilyLoading.dismiss();
        popAction();
      });
    } else if (type == QRScanType.QRScanTypeVirtualDev) {
      _bindVirtualDevWithDeviceId(pageUrl, popAction);
    } else if (type == QRScanType.QRSecurity) {
      _securityParse(pageUrl, originalStr, popAction);
    } else if (type == QRScanType.QRScanTypeNoNetwork ||
        type == QRScanType.QRScanTypeParseError ||
        type == QRScanType.QRSecurityInfoError) {
      QRScanToast().showErrorText(pageUrl);
    }
  }

  Future<void> _gotoBindingPage(
      String pageUrl, String scanCode, VoidCallback popAction) async {
    var parameters = <String, String>{'scanCode': scanCode};
    bool connectionStatus = await InternetUtil.checkIsReachableNetwork();
    if (!connectionStatus) {
      QRScanToast().showErrorText(QRScanErrorString.QR_NO_NETWORK_PROMPT);
      return;
    }
    popAction();
    _gotoPage(pageUrl, parameters);
  }

  void _gotoPage(String pageUrl, Map<String, String>? parameters) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_gotoPage', 'pageUrl': pageUrl, 'parameters': parameters});
    if (parameters == null || parameters.length == 0) {
      Vdn.goToPage(pageUrl)
          .then((Map<dynamic, dynamic> value) {})
          .catchError((dynamic e) {
        LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, dynamic>{
          'fn': '_gotoPage',
          'parameters == null catchError': e
        });
      });
    } else {
      Vdn.goToPage(pageUrl, params: parameters)
          .then((Map<dynamic, dynamic> value) {})
          .catchError((dynamic e) {
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: <String, dynamic>{'fn': '_gotoPage', 'catchError': e});
      });
    }
  }

  Future<void> _joinFamilySucessWithFamilyId(String familyId) async {
    // QRScanToast().showErrorText(QRScanTipsString.QR_SCA_ADD_FAMILY_SUCCES);
    // User.refreshUser().then((value) => {Family.setCurrentFamily(familyId)});
    try {
      await User.refreshUser();
      await Family.setCurrentFamily(familyId);
    } catch (e) {
      LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, dynamic>{
        'fn': '_joinFamilySucessWithFamilyId',
        'catchError': e
      });
    }
  }

  void _bindVirtualDevWithDeviceId(String deviceId, VoidCallback popAction) {
    //等待此
    String deviceDetailUrl =
        QRString.QR_SCAN_DEVICE_DETAIL_URL.replaceAll('%@', deviceId);
    popAction();
    _gotoPage(deviceDetailUrl, null);
    //不需要等待用户刷新完成
    User.refreshUser();
  }

  void _securityParse(
      String pageUrl, String originalStr, VoidCallback popAction) {
    try {
      var data = jsonDecode(originalStr);
      if (data == null || !(data is Map)) {
        QRScanToast().showErrorText(QRScanErrorString.QR_TASK_INFO_ERROR);
        return;
      }
      registerAndPush(data);
    } catch (e) {
      LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, dynamic>{
        'fn': '_securityParse_jsonDecode',
        'catch': e
      });
    }
  }

  Future<void> migrationTask(
      Map<dynamic, dynamic> map, String passWord, VoidCallback callback) async {
    bool connectionStatus = await InternetUtil.checkIsReachableNetwork();
    if (!connectionStatus) {
      QRScanToast().showErrorText(QRScanErrorString.QR_NO_NETWORK_ERROR);
      return;
    }
    LoginStatus status = await User.getLoginStatus();
    if (status.isLogin != true) {
      QRScanToast().showErrorText(QRScanErrorString.QR_NOT_LOGIN_PROMPT);
      return;
    }

    //请求参数
    Map<String, dynamic> paramMap = Map<String, dynamic>();
    paramMap['taskId'] = map['taskId'];
    paramMap['vCode'] = passWord;

    final _timestamp = HTTP.getTimestamp();
    AppInfoModel appInfoModel = await AppInfoPlugin.getAppInfo();
    String sign = HTTP.sign(METHOD.POST, ApiUrl.QR_SCAN_MIGRATION_URL, paramMap,
        appInfoModel.appId, appInfoModel.appKey, _timestamp);
    Map<String, dynamic> _headersInfo =
        await HTTP.headers({"sign": sign, 'timestamp': _timestamp});
    var response = await httpManager.postData(
      RequestUtil.getSerEnv() + '${ApiUrl.QR_SCAN_MIGRATION_URL}',
      params: paramMap,
      header: _headersInfo,
    );
    LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: {
      'fn': 'migrationTask',
      'result': "$response  paramMap $paramMap"
    });
    if (response != null && response is Map<String, dynamic>) {
      final QrScanResponse migrationResponse =
          QrScanResponse.fromJson(response);
      try {
        String? retCode = migrationResponse.retCode;
        if (retCode == null) {
          QRScanToast()
              .showErrorText(QRScanErrorString.QR_TASK_MIGRATION_ERROR);
          return;
        }
        if (retCode == QRScanRequestString.QR_SCAN_REQUEST_SUCCES) {
          LogHelper.debug(
              tag: QRScanLogString.tagQRScan,
              msg: {'migrationTask': 'User.refreshUser'});
          User.refreshUser();
          QRScanToast()
              .showErrorText(QRScanTipsString.QR_SCAN_MIGRATION_SUCCESS);
        } else if (retCode == QRScanRequestString.QR_SCAN_REQUEST_LOCK) {
          callback();
        } else if (retCode == QRScanRequestString.QR_SCAN_TASK_NO_EXIST) {
          QRScanToast().showErrorText(QRScanErrorString.QR_TASK_INFO_ERROR);
        } else if (retCode ==
            QRScanRequestString.QR_SCAN_TASK_EXECUTE_FAILURE) {
          QRScanToast()
              .showErrorText(QRScanErrorString.QR_TASK_MIGRATION_ERROR);
        } else if (retCode == QRScanRequestString.QR_SCAN_CODE_ERROR) {
          QRScanToast().showErrorText(QRScanErrorString.QR_TASK_CODE_ERROR);
        } else if (retCode == QRScanRequestString.QR_SCAN_OVER_MAX) {
          QRScanToast().showErrorText(QRScanErrorString.QR_TASK_OVER_ERROR);
        } else {
          QRScanToast().showErrorText(QRScanErrorString.QR_TASK_OTHER_ERROR);
        }
      } catch (e) {
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: {'fn': 'migrationTask', 'catch': e});
      }
    } else {
      QRScanToast().showErrorText(QRScanErrorString.QR_TASK_MIGRATION_ERROR);
    }
  }

  void registerAndPush(Map<dynamic, dynamic> data) {
    Vdn.goToPage(QRString.QR_SCAN_MIGRATION_STRING,
            params: data as Map<String, dynamic>)
        .catchError((e) {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': 'registerAndPush', 'catchError': e});
    });
  }
}
