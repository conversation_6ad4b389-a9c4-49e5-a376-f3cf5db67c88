import 'dart:convert';
import 'package:Appinfos/Appinfos.dart';
import 'package:Appinfos/AppinfosModel.dart';
import 'package:user/user.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:scan/util/qr_scan_string_util.dart';
import 'package:scan/util/qr_scan_sign_util.dart';
import 'package:scan/config/qr_sting.dart';
import 'package:scan/util/qr_log.dart';

class QRScanServerHeader {
  /// 应用唯一标识
  String appId;

  /// 应用的秘钥
  String appKey;

  /// 应用的版本信息字段
  String appVersion;

  /// 请求的用户令牌字段
  String accessToken;

  /// 用户中心的访问令牌字段
  String accountToken;

  /// 请求的时间戳字段
  String timestamp;

  /// 请求的客户端唯一标识
  String clientId;

  /// 请求的用户签名
  String sign;

  /// 请求发起的序列唯一标识
  String sequenceId;

  /// 语言信息字段
  String language;

  /// 时区字段
  String timeZone;

  /// Content-Type字段
  String contentType;

  QRScanServerHeader(
      this.appId,
      this.appKey,
      this.appVersion,
      this.accessToken,
      this.accountToken,
      this.timestamp,
      this.clientId,
      this.sequenceId,
      this.language,
      this.timeZone,
      this.contentType,
      this.sign);

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = new Map<String, dynamic>();
    map["appId"] = this.appId;
    map["appKey"] = this.appKey;
    map["appVersion"] = this.appVersion;
    map["accessToken"] = this.accessToken;
    map["accountToken"] = this.accountToken;
    map["timestamp"] = this.timestamp;
    map["clientId"] = this.clientId;
    map["sequenceId"] = this.sequenceId;
    map["language"] = this.language;
    map["timeZone"] = this.timeZone;
    map["Content-Type"] = this.contentType;
    map["sign"] = this.sign;
    return map;
  }

  // ignore: missing_return
  static Future<Map<String, dynamic>> signHeaderWithBody(
      Map<String, dynamic>? bodyDic) async {
    try {
      AppInfoModel appInfoModel = await AppInfoPlugin.getAppInfo();
      String accessTokenStr = '';
      String accountToken = '';
      try {
        OauthData oauthData = await User.getOauthData();
        accessTokenStr = oauthData.uhome_access_token;
        accountToken = oauthData.user_center_access_token;
      } catch (e) {
        rethrow;
      }

      String timestamp = new DateTime.now().millisecondsSinceEpoch.toString();
      String signStr = _getSignWithBody(
          bodyDic, timestamp, appInfoModel.appId, appInfoModel.appKey);
      QRScanServerHeader commonHeaderModel = QRScanServerHeader(
          appInfoModel.appId,
          appInfoModel.appKey,
          appInfoModel.appVersion,
          accessTokenStr,
          accountToken,
          timestamp,
          appInfoModel.clientId,
          _getSequenceId(),
          'zh_CN',
          DateTime.now().timeZoneName,
          'application/json;charset=utf-8',
          signStr);
      Map<String, dynamic> map = commonHeaderModel.toJson();
      return map;
    } catch (e) {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': 'signHeaderWithBody', 'catchError': e});
      rethrow;
    }
  }

  // ignore: missing_return
  static Future<Map<String, dynamic>> signHeaderWithBodyAndUrl(
      String url, Map<String, dynamic>? bodyDic) async {
    try {
      AppInfoModel appInfoModel = await AppInfoPlugin.getAppInfo();
      String accessTokenStr = '';
      String accountToken = '';
      try {
        OauthData oauthData = await User.getOauthData();
        accessTokenStr = oauthData.uhome_access_token;
        accountToken = oauthData.user_center_access_token;
      } catch (e) {
        rethrow;
      }
      String timestamp = new DateTime.now().millisecondsSinceEpoch.toString();
      String signStr = _getSignWithBodyAndUrl(
          bodyDic, url, timestamp, appInfoModel.appId, appInfoModel.appKey);
      if (signStr.length > (16 + 32)) {
        signStr = signStr.substring(16, 16 + 32);
      }
      QRScanServerHeader commonHeaderModel = QRScanServerHeader(
          appInfoModel.appId,
          appInfoModel.appKey,
          appInfoModel.appVersion,
          accessTokenStr,
          accountToken,
          timestamp,
          appInfoModel.clientId,
          _getUwsSequenceId(),
          'zh_cn',

          ///zh-cn zh_CN
          '8',
          'application/json;charset=utf-8',
          signStr);
      Map<String, dynamic> map = {
        'appId': commonHeaderModel.appId,
        'clientId': commonHeaderModel.clientId,
        'appVersion': commonHeaderModel.appVersion,
        'accessToken': commonHeaderModel.accessToken,
        'accountToken': commonHeaderModel.accountToken,
        'timestamp': commonHeaderModel.timestamp,
        'timeZone': 'GMT+8',
        'timezone': commonHeaderModel.timeZone,
        'language': commonHeaderModel.language,
        'sequenceId': commonHeaderModel.sequenceId,
        'sign': commonHeaderModel.sign,
        'appKey': commonHeaderModel.appKey,
        'Content-Type': 'application/json;charset=utf-8'
      };
      return map;
    } catch (e) {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': 'signHeaderWithBodyAndUrl', 'catchError': e});
      rethrow;
    }
  }

  // 智家APP 签名
  static Future<Map<String, dynamic>> signHeaderWithBodyAndUrlForZJ(
      String url, Map<String, dynamic>? bodyDic) async {
    try {
      final AppInfoModel appInfoModel = await AppInfoPlugin.getAppInfo();
      String accessTokenStr = '';
      String accountToken = '';
      try {
        final OauthData oauthData = await User.getOauthData();
        accessTokenStr = oauthData.uhome_access_token;
        accountToken = oauthData.user_center_access_token;
      } catch (e) {
        rethrow;
      }
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      String signStr = _getSignWithBodyAndUrl(
          bodyDic, url, timestamp, appInfoModel.appId, appInfoModel.appKey);
      QRScanServerHeader commonHeaderModel = QRScanServerHeader(
          appInfoModel.appId,
          appInfoModel.appKey,
          appInfoModel.appVersion,
          accessTokenStr,
          accountToken,
          timestamp,
          appInfoModel.clientId,
          _getSequenceId(),
          'zh_cn',
          '8',
          'application/json;charset=utf-8',
          signStr);
      Map<String, dynamic> map = {
        'appId': commonHeaderModel.appId,
        'clientId': commonHeaderModel.clientId,
        'appVersion': commonHeaderModel.appVersion,
        'accessToken': commonHeaderModel.accessToken,
        'accountToken': commonHeaderModel.accountToken,
        'timestamp': commonHeaderModel.timestamp,
        'timeZone': 'GMT+8',
        'timezone': commonHeaderModel.timeZone,
        'language': commonHeaderModel.language,
        'sequenceId': commonHeaderModel.sequenceId,
        'sign': commonHeaderModel.sign,
        'appKey': commonHeaderModel.appKey,
        'Content-Type': 'application/json;charset=utf-8'
      };
      return map;
    } catch (e) {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': 'signHeaderWithBodyAndUrl', 'catchError': e});
      rethrow;
    }
  }

  /// 带body和url签名
  static String _getSignWithBodyAndUrl(Map<String, dynamic>? bodyDic,
      String url, String timestamp, String appId, String appKey) {
    // Map转json字符串
    String bodyStr = json.encode(bodyDic);
    bodyStr = StringUtil.stringByTrimmingUselessCharacters(bodyStr);
    String urlStr = StringUtil.processUrlString(url);
    List parameters = [urlStr, bodyStr, appId, appKey, timestamp];
    return QRScanSignUtil.generate_sha256(
        StringUtil.converListToString(parameters));
  }

  /// 带body签名
  static String _getSignWithBody(Map<String, dynamic>? bodyDic,
      String timestamp, String appId, String appKey) {
    // Map转json字符串
    String bodyStr = json.encode(bodyDic);
    bodyStr = StringUtil.stringByTrimmingUselessCharacters(bodyStr);
    List parameters = [bodyStr, appId, appKey, timestamp];
    return QRScanSignUtil.generate_md5(
        StringUtil.converListToString(parameters));
  }

  ///
  static String _getSequenceId() {
    DateTime dateTime = DateTime.now();
    var year = dateTime.year;
    var month = dateTime.month;
    var day = dateTime.day;
    var hour = dateTime.hour;
    var minute = dateTime.minute;
    var second = dateTime.second;
    return '$year + $month + $day + $hour + $minute + $second + 00001';
  }

  ///
  static String _getUwsSequenceId() {
    DateTime dateTime = DateTime.now();
    var year = dateTime.year;
    var month = dateTime.month;
    var day = dateTime.day;
    var hour = dateTime.hour;
    var minute = dateTime.minute;
    var second = dateTime.second;
    return '$year' +
        StringUtil.gainFormatString(month) +
        StringUtil.gainFormatString(day) +
        StringUtil.gainFormatString(hour) +
        StringUtil.gainFormatString(minute) +
        StringUtil.gainFormatString(second) +
        '00001';
  }
}
