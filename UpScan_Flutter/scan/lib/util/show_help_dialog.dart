import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

const _helpTitle = "扫码小贴士";
const _bindingContent = "请在家电机身正面、侧面等位置找到图示中的标贴，扫描标贴中二维码或条形码";

class ShowHelpDialog extends Dialog {
  @override
  Widget build(BuildContext context) {
    double width = ScreenUtil().screenWidth;
    double height = ScreenUtil().screenHeight;
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: width - (width>height?110.w:50.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                ),
                child: Column(
                  children: [
                    Container(
                    height: width>height?20.w:55.w,
                      child: Center(
                          child: Text(
                        _helpTitle,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 20.sp),
                      )),
                    ),
                    Container(height: 0.5.w, color: Colors.grey),
                    Container(
                      padding: EdgeInsets.fromLTRB(16, 13, 16, 20),
                      child: Text(
                        _bindingContent,
                        style: TextStyle(fontSize: 14.sp),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(
                          left: 14.w, right: 16.w, bottom: 10.w),
                      child: Image.asset(
                        'images/bind_image.png',
                        width: width - (width>height?110.w:50.w),
                        fit: BoxFit.fitWidth,
                        package: 'scan',
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(top: 10.w),
                  child: Image.asset(
                    'images/bind_cancel.png',
                    width: 28,
                    height: 28,
                    package: 'scan',
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
