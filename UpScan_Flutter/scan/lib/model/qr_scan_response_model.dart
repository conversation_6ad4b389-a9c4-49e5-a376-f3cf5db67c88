import '../config/qr_sting.dart';
import '../util/common_util.dart';
import '../util/qr_log.dart';

class QrScanResponse {
  /// fromJson
  QrScanResponse.fromJson(Map<String, dynamic> json) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': 'QrScanResponse from json', 'data': '$json'});
    if (json.containsKey(QRScanRequestString.QR_SCAN_REQUEST_RETCODE)) {
      retCode = CommonUtil.convertType<String?>(
          json[QRScanRequestString.QR_SCAN_REQUEST_RETCODE], null);
    }
    if (json.containsKey(QRScanRequestString.QR_SCAN_REQUEST_DATA)) {
      // final Map<String, dynamic> dataJson =
      //     CommonUtil.convertType<Map<String, dynamic>>(
      //         json[QRScanRequestString.QR_SCAN_REQUEST_DATA],
      //         <String, dynamic>{});
      // data = QrScanResponseData.fromJson(dataJson);
      // data数据有可能是String，也有可能是Map
      data = json[QRScanRequestString.QR_SCAN_REQUEST_DATA];
    }
  }

  /// retCode
  String? retCode;

  /// data
  dynamic data;
}

class QrScanResponseData {
  /// fromJson
  QrScanResponseData.fromJson(Map<String, dynamic> json) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': 'QrScanResponseData from json', 'data': '$json'});
    if (json.containsKey(QRScanRequestString.QR_SCAN_REQUEST_LONG_LINK)) {
      longLink = CommonUtil.convertType<String?>(
          json[QRScanRequestString.QR_SCAN_REQUEST_LONG_LINK], null);
    }
    if (json.containsKey(QRScanRequestString.QR_SCAN_REQUEST_JUMP_URL)) {
      jumpUrl = CommonUtil.convertType<String?>(json[QRScanRequestString.QR_SCAN_REQUEST_JUMP_URL], null);
    }
    if (json.containsKey(QRScanRequestString.QR_SCAN_REQUEST_FAMILY_ID)) {
      familyId = CommonUtil.convertType<String?>(json[QRScanRequestString.QR_SCAN_REQUEST_FAMILY_ID], null);
    }
    if (json.containsKey(QRScanRequestString.QR_SCAN_REQUEST_TASK_STATE)) {
      taskState = CommonUtil.convertType<int?>(json[QRScanRequestString.QR_SCAN_REQUEST_TASK_STATE], null);
    }
  }
  String? longLink;
  String? jumpUrl;
  String? familyId;
  int? taskState;
}
