import '../config/qr_sting.dart';
import '../util/common_util.dart';
import '../util/qr_log.dart';

class CommonResourceResult {
  /// fromJson
  CommonResourceResult.fromJson(Map<String, dynamic> json) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': 'QrScanCommonResourceResult from json', 'data': '$json'});
    if (json.containsKey('retCode')) {
      retCode = CommonUtil.convertType<String?>(json['retCode'], null);
    }
    if (json.containsKey('retData')) {
      final Map<String, dynamic> data =
          CommonUtil.convertType<Map<dynamic, dynamic>>(
              json['retData'], <dynamic, dynamic>{}).cast<String, dynamic>();
      retData = CommonResourceData.fromJson(data);
    }
  }

  /// retCode
  String? retCode;

  /// retData
  CommonResourceData? retData;
}

class CommonResourceData {
  CommonResourceData.fromJson(Map<String, dynamic> map) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': 'QrScanCommonResourceData from json', 'data': '$map'});
    if (map.containsKey('link')) {
      link = CommonUtil.convertType<String?>(map['link'], null);
    }
  }
  String? link;
}
