package com.uplus.upscan.core.handlers;

/**
 * 扫码结果状态码枚举
 * 定义扫码处理过程中可能出现的各种状态
 */
public enum UpScanResultCode {
    
    /**
     * 成功
     */
    SUCCESS("00000", "成功"),
    
    /**
     * 用户未登录
     */
    USER_NOT_LOGIN("10001", "用户未登录"),
    
    /**
     * 没有网络
     */
    NO_NETWORK("10002", "没有网络"),
    
    /**
     * 网络请求失败
     */
    NETWORK_REQUEST_FAILED("10003", "网络请求失败"),
    
    /**
     * 数据解析失败
     */
    DATA_PARSE_FAILED("10004", "数据解析失败");
    
    private final String code;
    private final String message;
    
    UpScanResultCode(String code, String message) {
        this.code = code;
        this.message = message;
    }
    
    /**
     * 获取状态码
     * @return 状态码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取状态描述
     * @return 状态描述
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * 根据状态码获取枚举
     * @param code 状态码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static UpScanResultCode fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (UpScanResultCode resultCode : values()) {
            if (resultCode.code.equals(code)) {
                return resultCode;
            }
        }
        return null;
    }
    
    /**
     * 判断是否成功
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
    
    @Override
    public String toString() {
        return "UpScanResultCode{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
