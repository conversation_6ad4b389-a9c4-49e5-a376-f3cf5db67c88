import '../config/qr_sting.dart';
import '../util/common_util.dart';
import '../util/qr_log.dart';

class QrAlbumResultData {
  /// fromJson
  QrAlbumResultData.fromJson(Map<String, dynamic> json) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': 'QrAlbumResultData from json', 'data': '$json'});
    if (json.containsKey('path')) {
      path = CommonUtil.convertType<String>(json['path'], '');
    }
  }

  /// retData
  String? path;
}
