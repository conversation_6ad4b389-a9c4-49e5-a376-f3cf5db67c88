import 'dart:async';

import 'package:family/family.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:message/message.dart';
import 'package:message/msgmodel.dart';
import 'package:scan/pages/show_tips.dart';
import 'package:scan/util/qr_log.dart';
import 'package:vdn/vdn.dart';

import '../common_widget/qr_scan_toast.dart';
import '../config/qr_sting.dart';
import '../handler/qr_scan_goto_page_handler.dart';
import '../util/common_util.dart';

class SecurityMigration extends StatefulWidget {
  final Map<dynamic, dynamic> map;

  const SecurityMigration({required this.map});

  @override
  State<SecurityMigration> createState() => _SecurityMigrationState();
}

class _SecurityMigrationState extends State<SecurityMigration> {
  bool canCommit = false;
  TextEditingController _controller = TextEditingController();
  StreamSubscription? _userRefreshListen;
  @override
  void initState() {
    super.initState();
    _controller.addListener(() {
      if (_controller.text.length == 0) {
        canCommit = false;
      } else {
        canCommit = true;
      }
      setState(() {});
    });
    String stateStr = CommonUtil.convertType<String>(widget.map[QRScanRequestString.QR_SCAN_REQUEST_TASK_STATE], '-1');
    int state = int.tryParse(stateStr) ?? -1;
    if (-1 == state) {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: <String, String>{'task state error': '$state'});
    }
    WidgetsBinding.instance!.addPostFrameCallback((mag) {
      if (state == SecurityTaskState.SecurityPause) {
        showTips(CommonUtil.convertType<String>(widget.map[QRScanRequestString.QR_SCAN_SECURITY_PHONE], ''));
      }
    });
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'UserRefreshCompletedMessage': 'register'});
    _userRefreshListen = Message.listen<UserRefreshCompletedMessage>((event) {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'migrationTask': 'listen UserRefreshCompletedMessage${widget.map[QRScanRequestString.QR_SCAN_REQUEST_FAMILY_ID]}'});
      Family.setCurrentFamily(
          CommonUtil.convertType<String>(widget.map[QRScanRequestString.QR_SCAN_REQUEST_FAMILY_ID], '')).then(
              (value) {
            LogHelper.debug(
                tag: QRScanLogString.tagQRScan,
                msg: {'migrationTask': 'setCurrentFamily'});
            Vdn.goToPage(QRString.QR_SCAN_CHANGE_TAB);
          });
    });
  }

  @override
  Widget build(BuildContext context) {
    //此处计算是根据裁剪框的大小设定的
    ScreenUtil.init(
        BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width,
            maxHeight: MediaQuery.of(context).size.height),
        designSize: Size(375, 812),
        orientation: Orientation.portrait);
    return Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
            title: Text(
              "设备迁移",
              style: TextStyle(
                fontSize: 17.sp,
                color: Colors.black,
              ),
            ),
            centerTitle: true,
            elevation: 0,
            leading: BackButton(
              onPressed: () {
                FocusScope.of(context).requestFocus(FocusNode());
                Navigator.pop(context);
              },
              color: Color.fromRGBO(0, 0, 0, 0.93),
            ),
            systemOverlayStyle:
                SystemUiOverlayStyle(systemNavigationBarColor: Colors.white)),
        backgroundColor: Color(0xfff5f5f5),
        body: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: Stack(
            children: [
              Container(),
              SingleChildScrollView(
                reverse: false,
                child: childWidget(),
              )
            ],
          ),
        ));
  }

  void showTips(String? phone) {
    Future.delayed(Duration(milliseconds: 2), () {
      showTipsDialog(context, phone);
    });
  }

  Widget _buildInputWidget() {
    return Container(
      height: 44.w,
      margin: EdgeInsets.only(top: 32.w, left: 16.w, right: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
              child: TextField(
            enableInteractiveSelection: false,
            style: TextStyle(
                fontSize: 15.sp,
                color: Color.fromRGBO(0, 0, 0, 0.93),
                fontWeight: FontWeight.w500),
            autofocus: false,
            textCapitalization: TextCapitalization.none,
            maxLines: 1,
            keyboardType: TextInputType.number,
            controller: _controller,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp('[0-9]')),
              LengthLimitingTextInputFormatter(8),
            ],

            decoration: InputDecoration(
              filled: true,
              fillColor: Color(0xffEEEEEE),
              hintText: "输入8位数迁移码",
              hintStyle: TextStyle(
                  fontSize: 15.sp,
                  color: Color.fromRGBO(0, 0, 0, 0.26),
                  fontWeight: FontWeight.w500),
              suffixIcon: canCommit
                  ? IconButton(
                      icon: Image.asset(
                        'images/cancel.png',
                        width: 20.w,
                        height: 20.w,
                        fit: BoxFit.cover,
                        package: 'scan',
                      ),
                      onPressed: () {
                        _controller.clear();
                      })
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.w)),
                  borderSide: BorderSide(
                    color: Colors.transparent,
                  ),
              ),
              contentPadding: EdgeInsets.only(
                left: 10,
                top: 0,
                bottom: 0, // HERE THE IMPORTANT PART
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.w)),
                borderSide: BorderSide(
                  color: Colors.transparent,
                ),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.w)),
                borderSide: BorderSide(
                  color: Colors.transparent,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8.w)),
                borderSide: BorderSide(
                  color: Colors.transparent,
                ),
              ),
            ),
          ))
        ],
      ),
    );
  }

  Widget _buildActionWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Container(
          width: 160.w,
          height: 44.w,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(24.w)),
              border: Border.all(color: Color(0xff2283E2), width: 1.w)),
          child: TextButton(
            style: ButtonStyle(
                shape: MaterialStateProperty.all(RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24.w))),
                backgroundColor: MaterialStateProperty.all(Colors.white),
                side: MaterialStateProperty.all(
                  BorderSide(color: Color(0xff2283E2), width: 0.3.w),
                )),
            child: Center(
              child: Text("暂不迁移",
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 17.sp, color: Color(0xff2283E2))),
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
        SizedBox(
          width: 23.w,
        ),
        Container(
          width: 160.w,
          height: 44.w,
          child: TextButton(
              style: ButtonStyle(
                  enableFeedback: canCommit,
                  shape: MaterialStateProperty.all(RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24.w))),
                  backgroundColor: MaterialStateProperty.all(canCommit
                      ? Color(0xff2283E2)
                      : Color.fromRGBO(0, 0, 0, 0.13))),
              child: Text(
                "确定迁移",
                style: TextStyle(
                    color: canCommit
                        ? Colors.white
                        : Color.fromRGBO(0, 0, 0, 0.39),
                    fontSize: 17.sp),
              ),
              onPressed: _throttle(() async {
                onSure();
                await Future.delayed(Duration(milliseconds: 2000));
              }) as Function()?),
        ),
      ],
    );
  }

  Function _throttle(
    Future Function() func,
  ) {
    bool enable = true;
    Function target = () {
      if (enable == true) {
        enable = false;
        func().then((_) {
          enable = true;
        });
      }
    };
    return target;
  }

  void onSure(){
    String passWord = _controller.text;

    if (passWord.isEmpty || passWord.length < 8) {
      QRScanToast().showErrorText(QRScanErrorString.QR_TASK_CODE_SHORT);
      return;
    }
    QRScanGotoPageHandler().migrationTask(widget.map, passWord, () {
      showTips(CommonUtil.convertType<String>(widget.map[QRScanRequestString.QR_SCAN_SECURITY_PHONE], ''));
    });
  }
  Widget childWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInputWidget(),
        SizedBox(
          height: 64.w,
        ),
        _buildActionWidget(),
      ],
    );
  }
  @override
  void dispose() {
    _userRefreshListen?.cancel();
    super.dispose();

  }
}
