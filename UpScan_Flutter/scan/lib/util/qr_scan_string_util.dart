import 'common_util.dart';

class StringUtil{
  //字符串帮助类
  static bool isBlankString(String? str){
    if(str == null){
      return true;
    }
    if(str.isEmpty){
      return true;
    }
    str = str.trim();
    if(str.length == 0){
      return true;
    }
    return false;
  }

  static String stringByTrimmingUselessCharacters(String str) {
    str = str.replaceAll('\n', '');
    str = str.replaceAll('\r', '');
    str = str.replaceAll(' ', '');
    str = str.replaceAll('\b', '');
    str = str.replaceAll('\t', '');
    return str;
  }

  /// url添加/前缀
  static String processUrlString(String url) {
    String urlString = '';
    if (url == null) {
      urlString = '';
    } else {
      if (url.startsWith("/")) {
        urlString = url;
      } else {
        urlString = '/' + url;
      }
    }
    return urlString;
  }

  /// list转String
  static String converListToString(List list) {
    String str = '';
    list.forEach((element) {
      final String elementStr = CommonUtil.convertType<String>(element, '');
      str = str + stringByTrimmingUselessCharacters(elementStr);
    });
    return str;
  }

  static String gainFormatString(int num){
    
    if(num < 10){
      return '0$num';
    }
    return '$num';
  }

  static bool gainBoolValue(String? value, bool defaultVale){
    if (isBlankString(value)) {
      return defaultVale;
    }else{
      return int.parse(value!) == 0 ? false : true;
    }
  }
}