class ApiUrl {
  //前缀
  static const String URL_HEAD = "https://zj.haier.net";
  //验收
  static const UHOME_NET_YANSHOU_HOST = 'https://zj-yanshou.haier.net';
  //获取长链地址
  static const String QRSCAN_LONG_LINK =
      URL_HEAD + "/omsappapi/omsva/secuag/getLongLink";

  //扫码加入家庭地址
  static const String QRSCAN_ADD_FAMILY_LINK =
      URL_HEAD + "/emuplus/family/v1/family/scan/qrcode";

  //扫码加入家庭地址
  static const String QRSCAN_VIRTUAL_DEVICE_LINK =
      'https://api.haigeek.com' + '/vdmgmt/user/devices/bind';

  //三翼鸟前缀
  static const String URL_SYN_HEAD = "https://uhome.haier.net/";

  //三翼鸟获取长链地址
  static const String QRSCAN_SYN_LONG_LINK =
      URL_SYN_HEAD + "/omsappapi/omsva/secuag/getLongLink";

  //安防设备任务状态查询
  static const String QR_SCAN_SECURITY_URL =
      '/api-gw/wisdomdevice/device/task/anonymous/query';

  //安防迁移
  static const String QR_SCAN_MIGRATION_URL =
      '/api-gw/wisdomdevice/device/task/execute';
  //适配扫地机器人，查询长链接
  static const String QR_SCAN_GET_LONG_PAGE_URL =
      '/api-gw/zjBaseServer/scan/jump';

  //扫码查询设备信息v3(增加查询未接入海极网逻辑)
  static const String QR_SCAN_GET_DEVICE_MODE_INFO =
      '/api-gw/wisdomdevice/device/scan/code/v3/model/info/query';

  //获取二维码详细信息
  static const String QR_SCAN_GET_QRCODE_INFO =
      '/api-gw/wisdomfamily/family/v1/qrcode/info';

  //扫描二维码加入家庭
  static const String QR_SCAN_INVITE_FAMILY =
      '/api-gw/wisdomfamily/family/refactor/v1/family/scan/qrcode';
}
