class QRString {
  static const String QR_SCAN_STRING = 'flutter://qrscan';
  static const String QR_SCAN_GENERAL_STRING = 'flutter://scan/qrgeneralscan';
  static const String QR_SCAN_SYN_STRING = 'flutter://scan/qrsynscan';
  static const String QR_SCAN_MIGRATION_STRING = 'flutter://scan/migration';
  static const String QR_SCAN_HELP_PAGE_STRING =
      'https://zjrs.haier.net/zjapp/scan/scanStatic.html';
  static const String QR_SCAN_CLOUD_CLOTH_WASH_PAGE_URL_SHENGCHAN =
      'https://cloudclothwash.haier.net:9413/cloudclothwash-wm/index.html?container_type=3&hidesBottomBarWhenPushed=1&wash_imgurl=%@&hybrid_navbar_hidden=true&#/imgRecognition';
  static const String QR_SCAN_CLOUD_CLOTH_WASH_PAGE_URL_YANSHOU =
      'https://cloudclothwash2.haier.net:9413/cloudclothwash-wm/index.html?container_type=3&hidesBottomBarWhenPushed=1&wash_imgurl=%@&hybrid_navbar_hidden=true&#/imgRecognition';

  static const String QR_SCAN_LOGIN_MARK = 'uplus://login/';
  static const String QR_SCAN_SHORT_LINK_MARK = 'z.haier.net/U/';
  static const String QR_SCAN_SHORT_LINK_MARK1 = 'zj9.co/U/';
  static const String QR_SCAN_YANSHOU_SHORT_LINK_MARK1 = 'z-ys.haier.net/U/';
  static const String QR_SCAN_ADD_FANILY_MARK = 'uplus://joinFamily/';
  static const String QR_SCAN_BCOID_CODE_MARK = 'oid.haier.com';
  static const String QR_SCAN_SECURITY_CODE_MARK = 'MIGRATE_QR\$';
  static const String QR_SCAN_BCENERGY_EFFICIENCY_CODE_MARK = 'bbqk.com';
  static const String QR_SCAN_LOGIN_PAGE_URL =
      'mpaas://tvAuthorization?loginToken=%@&entry=1';
  static const String QR_SCAN_BINDING_PAGE_URL =
      'https://uplus.haier.com/uplusapp/bind/scanbindentrance.html';
  static const String QR_SCAN_BINDING_PAGE_URL_FOR_H5 =
      'https://uplus.haier.com/uplusapp/main/qrcodescan.html?entranceType=scan';
  static const String QR_SCAN_DEVICE_DETAIL_URL =
      'http://uplus.haier.com/uplusapp/DeviceList/DetailView.html?deviceId=%@&bindSuccess=true';
  static const String QR_SCAN_WASHCALL_MARK =
      'app.mrhi.cn/download/app/washcall/';
  static const String QR_SCAN_WASHCALL_OTHER_MARK =
      'uhome.haier.net/download/app/washcall/';
  static const String QR_SCAN_WASHCALL_URL =
      'https://www.sharewash.cn/saywash/WashCallZJ/index.html?needAuthLogin=1&';
  static const String QR_SCAN_PRODUCTION_LONG_LINK_MARK =
      'https://zjrs.haier.net/guide/index.html';
  static const String QR_SCAN_ACCEPTANCE_LONG_LINK_MARK =
      'https://ys-zjrs.haier.net/guide/index.html';
  static const String QR_SCAN_CHANGE_TAB =
      "https://uplus.haier.com/uplusapp/smart/index.html";

  //虚拟设备标识
  static const String QR_SCAN_VIRTUAL_DEV_MARK = 'virtualdev';
}

class QRContentString {
  static const String QR_NO_PHOTO_PERMISSIONS_TIP =
      '请在iphone的"设置-隐私-相机"选项中，允许海尔智家访问您的相机';
  static const String QR_NO_PHOTO_ANDROID_PERMISSIONS_TIP =
      '请在系统设置中开启相机权限，开通后您可以使用相机';
  static const String QR_NO_ALBUM_PERMISSIONS_TIP = '请允许海尔智家访问您的相册';
  static const String QR_NO_ALBUM_PERMISSIONS_TITLE = '相册权限申请';
  static const String QR_NO_PHOTO_PERMISSIONS = '相机权限不可用';
  static const String QR_SCAN_CONTENT = '支持海尔相关扫码\n';
  static const String QR_SCAN_BINDING = '添加设备';
  static const String QR_SCAN_OTHER_DES = '/多屏登录/添加家人/商品、食材码等';
  static const String QR_SCAN = '扫一扫';
  static const String QR_SCAN_ALBUM = '相册';
  static const String QR_SCAN_CANCEL = '取消';
  static const String QR_SCAN_SETTING = '去设置';
  static const String QR_SCAN_TAKEPHOTO = '拍照识衣';
  static const String QR_SCAN_TORCH = '手电筒';
  static const String QR_SCAN_HELP = '可以扫什么';
  static const String QR_SCAN_SYN_CONTENT = '支持扫描店铺内场景/商品二维码下单';
}

class QRScanErrorString {
  static const String QR_NOT_LOGIN_PROMPT = '用户未登录';
  static const String QR_NO_SCAN_RESULT = '未发现二维码/条形码';
  static const String QR_NO_PARSE_PROMPT = '不支持该二维码/条形码';
  static const String QR_NO_NETWORK_PROMPT = '当前服务不可用';
  static const String QR_NO_NETWORK_ERROR = '网络不可用';
  static const String QR_VIRTUAL_DEVICE_CREATE_ERROR = '虚拟设备创建失败，请重试';
  static const String QR_ADD_FAMILY_DISSOLUTION = '家庭解散，无法加入';
  static const String QR_ADD_FAMILY_NO_SUPPORT = '家庭不支持二维码功能';
  static const String QR_ADD_FAMILY_OVERDUE = '二维码已失效，无法加入';
  static const String QR_ADD_FAMILY_ERROR = '家庭二维码异常，请更换二维码';
  static const String QR_ADD_FAMILY_MEMBER_OVERFLOW_ERROR = '家庭成员数达到上限';
  static const String QR_ADD_FAMILY_OVERFLOW_ERROR = '您加入的家庭数达到上限';
  static const String QR_ADD_FAMILY_ALREADY_INVITED = '您已加入过该家庭';
  static const String QR_ADD_FAMILY_NEED_REFRESH = '请联系对方刷新二维码后重新扫描';
  static const String QR_ADD_FAMILY_TITLE_INVALID_CODE = '二维码失效';
  static const String QR_ADD_FAMILY_TITLE_FAILED = '无法加入该家庭';
  static const String QR_NOT_GAIN_PHOTO = '未获取到拍摄照片';
  static const String QR_TASK_INFO_ERROR = '迁移任务不存在';
  static const String QR_TASK_MIGRATION_ERROR = '迁移任务失败';
  static const String QR_TASK_CODE_ERROR = '迁移码错误请重新输入';
  static const String QR_TASK_CODE_SHORT = '请输入8位迁移码';
  static const String QR_TASK_OVER_ERROR = '超过用户绑定设备数量限制，请联系客服处理';
  static const String QR_TASK_OTHER_ERROR = '任务迁移失败，请联系客服处理';
}

class QRScanRequestString {
  static const String QR_SCAN_REQUEST_RETCODE = 'retCode';
  static const String QR_SCAN_REQUEST_RETINFO = 'retInfo';
  static const String QR_SCAN_REQUEST_DATA = 'data';

  static const String QR_SCAN_REQUEST_SUCCES = '00000';
  static const String QR_SCAN_REQUEST_LOCK = '1080010';
  static const String QR_SCAN_CODE_ERROR = '1080011';
  static const String QR_SCAN_TASK_NO_EXIST = '1080012';
  static const String QR_SCAN_TASK_EXECUTE_FAILURE = '1080021';
  static const String QR_SCAN_OVER_MAX = '1080020';
  static const String QR_SCAN_DATA_LINK = 'link';

  static const String QR_SCAN_REQUEST_LONG_LINK = 'longLink';
  static const String QR_SCAN_REQUEST_JUMP_URL = 'jumpUrl';
  static const String QR_SCAN_REQUEST_FAMILY_ID = 'familyId';
  static const String QR_SCAN_REQUEST_TASK_STATE = 'taskState';
  static const String QR_SCAN_SECURITY_PHONE = 'assistantNumber';

  static const String QR_SCAN_REQUEST_TYPE_ID = 'tyid';
  static const String QR_SCAN_REQUEST_PRODUCT_CODE = 'pro';
  static const String QR_SCAN_REQUEST_DEVICE_ID = 'deviceId';
}

class QRScanRequestErrorCode {
  static const String QR_SCAN_ADD_FAMILY_ERROR_E31108 = 'E31108';
  static const String QR_SCAN_ADD_FAMILY_ERROR_E31137 = 'E31137';
  static const String QR_SCAN_ADD_FAMILY_ERROR_E31138 = 'E31138';
  static const String QR_SCAN_ADD_FAMILY_ERROR_E31139 = 'E31139';
  static const String QR_SCAN_ADD_FAMILY_ERROR_E31405 = 'E31405';
  static const String QR_SCAN_ADD_FAMILY_ERROR_E31406 = 'E31406';
  static const String QR_SCAN_ADD_FAMILY_ERROR_E31105 = 'E31105';
}

class QRScanTipsString {
  static const String QR_SCA_ADD_FAMILY_SUCCES = '成功加入该家庭';
  static const String QR_SCAN_MIGRATION_SUCCESS = '完成数据迁移';
}

class QRScanFamilyPrivacyAgreementString {
  static const String QR_SCA_FAMILY_PRIVACY_TITLE = '家庭服务功能须知';
  static const String QR_SCA_FAMILY_PRIVACY_CONTENT =
      '\n您邀请家人对方同意加入后，或者您加入其他人的家庭后，家庭中所有用户都可以控制家庭中每个成员绑定的家电设备，使用家庭应用进行食材管理，并查看所有成员绑定的设备数据(包括历史数据、健康相关的隐私数据)。被邀请进入的家庭成员也有权限邀请新的家庭成员，新加入的家庭成员和您具有一样的查看、控制权限，家庭中的每个成员都可以自己退出，管理员可以删除成员。';
  static const String QR_SCA_FAMILY_PRIVACY_ADDITIONAL_CONTENT = '\n您是否知悉并同意？';
  static const String QR_SCA_FAMILY_PRIVACY_SURE = '同意';
  static const String QR_SCA_FAMILY_PRIVACY_CANCEL = '拒绝';
}

class QRScanTraceCodeString {
  ///扫码页点位
  static const String QR_SCAN_TRACE_CODE_SCAN_PAGE = 'MB17693';
  static const String QR_SCAN_TRACE_CODE_PHOTO = 'MB18032';
  static const String QR_SCAN_TRACE_CODE_SCAN_SUCESS = 'MB18033';
  static const String QR_SCAN_TRACE_CODE_BACK = 'MB18034';
  static const String QR_SCAN_TRACE_CODE_PHOTO_CANCEL = 'MB18036';
  static const String QR_SCAN_TRACE_CODE_PHOTO_CHANGE = 'MB18037';

  static const String QR_SCAN_TRACE_CODE_PAGE_LOAD_TIME = 'F_Scan'; // 页面加载时长
}

class QRScanLogString {
  // log的tag
  static const String tagQRScan = "[scan]:";
  static const String tagQRSynScan = "[synScan]:";
  static const String packageName = 'scan';
}

class SecurityTaskState {
  static const int SecurityNormal = 0; //待交割
  static const int SecurityDone = 1; // 已完成、
  static const int SecurityCancel = 2; //已取消
  static const int SecurityPause = 3; //已 锁定
}

class CameraPermissionConfig {
  static const String QR_SCAN_CAMERA_PERMISSION_OPEN = '去开启';
  static const String QR_SCAN_CAMERA_PERMISSION_TIPS =
      '开启相机权限，以便使用扫一扫功能（开启方法：去开启-权限-相机-允许相机权限）';
  static const String QR_SCAN_CAMERA_PERMISSION_CONFIG_URL =
      'http://uplus.haier.com/uplusapp/main/appPermission.html';
}
