import 'dart:io';
import 'dart:ui';
import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:log/log.dart';
import 'package:log/log_modle.dart';
import 'package:message/message.dart';
import 'package:message/msgmodel.dart';
import 'package:uppermission/requestresult.dart';
import 'package:vdn/back.dart';
import '../config/qr_general_scan_config.dart';
import 'package:r_scan/r_scan.dart';
import 'package:scan/config/qr_sting.dart';
import 'package:vdn/vdn.dart';
import '../model/qr_scan_album_model.dart';
import '../model/qr_scan_query_permission_model.dart';
import '../util/common_util.dart';
import '../util/show_help_dialog.dart';
import 'qr_dialog.dart';
import 'package:scan/util/qr_scan_string_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:scan/common_widget/qr_scan_toast.dart';
import 'package:scan/util/qr_scan_common_util.dart';
import 'package:uppermission/uppermission.dart';
import '../util/qr_log.dart';

typedef QRGeneralScanBackFunc = void Function(Map result);
typedef QRGeneralScanResultHandler = void Function(List<RScanResult> result,
    QRScanParametersConfig config, QRGeneralScanBackFunc popAction);

List<RScanCameraDescription>? rScanCameras;

class QRGeneralScanPage extends StatefulWidget {
  final QRScanParametersConfig config;
  final QRGeneralScanResultHandler scanResult;
  QRGeneralScanPage({required this.config, required this.scanResult});

  @override
  _QRGeneralScanPageState createState() => _QRGeneralScanPageState(scanResult);
}

class _QRGeneralScanPageState extends State<QRGeneralScanPage>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  RScanCameraController? _controller;
  bool isFirst = true;
  Timer? _timer;
  StreamController<dynamic>? _streamController;
  // 是否正在识别相册的图片
  bool _isAlbumScan = false;
  AnimationController? _animationController;
  // 扫一扫的线是否需要动画
  bool _isAnimatedLine = true;
  QRGeneralScanResultHandler scanResult;
  // 是否展示相机设置提示
  bool _cameraTipShow = false;
  _QRGeneralScanPageState(this.scanResult);
  StreamSubscription? _resumeMessage;
  StreamSubscription? _pauseMessage;
  // 相机是否可用，是否已被初始化
  bool _cameraAvailable = false;

  bool _flashMode = false;

  // 是否打开相册识别，用于解决iOS端侧滑返回后扫码框不显示扫描线问题
  bool _openAlbumForIOS = false;

  void initCamera() {
    LogHelper.debug(
      tag: QRScanLogString.tagQRScan,
      msg: <String, Object>{
        'fn': 'initCamera',
        '_cameraAvailable': _cameraAvailable
      },
    );
    if (rScanCameras == null || rScanCameras!.length == 0) {
      _requestPermissionFunction('camera', allowedCallback: () {
        _reloadScanContext();
      });
    }
    _initCameraController();
  }

  Future<void> _initCameraController() async {
    if (rScanCameras != null && rScanCameras!.length > 0) {
      LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, String>{
        'fn': '_initCameraController',
        'before': '_cameraAvailable: $_cameraAvailable'
      });
      if (_cameraAvailable) {
        return;
      }

      _cameraAvailable = true;
      LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, String>{
        'fn': '_initCameraController',
        'after': '_cameraAvailable: $_cameraAvailable'
      });
      _controller = RScanCameraController(
          rScanCameras![0], RScanCameraResolutionPreset.high)
        ..addListener(() {
          final List<RScanResult>? result = _controller!.result;
          LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: <String, Object?>{
              'fn': 'general_initCameraController',
              'result': result,
              '_controller': _controller,
              'isFirst': isFirst
            },
          );
          if (result != null) {
            if (isFirst) {
              LogHelper.debug(
                tag: QRScanLogString.tagQRScan,
                msg: <String, Object>{
                  'fn': 'general_initCameraController',
                  'scanResult': scanResult
                },
              );
              scanResult(result, widget.config,
                  (Map<dynamic, dynamic> map) async {
                // todo
                // await Future<void>.delayed(const Duration(milliseconds: 100));
                var data = map['result'];
                _disposeEvent();
                if (data is List) {
                  Vdn.close(result: <String, dynamic>{
                    'scanResult': result.first.message
                  });
                } else {
                  Vdn.close(result: <String, dynamic>{'scanResult': data});
                }
              });
              isFirst = false;
              //重置扫码
              resetScan();
            }
          }
        })
        ..initialize().then((_) {
          if (!mounted) {
            return;
          }
          setState(() {});
        });
      _flashMode = (await _controller?.getFlashMode())!;
    }
  }

  void resetScan() {
    if (_timer == null) {
      _timer = Timer.periodic(Duration(milliseconds: 3000), (t) {
        isFirst = true;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    BackKeyManager.disallowSystemBack();
    BackKeyManager.registerEventChannel((event) {
      Log.printLog(LogModle('debug', '[scan]', 'scan backPress'));
      _backAction();
    });
    WidgetsBinding.instance.addObserver(this);
    _streamController = StreamController<bool>();

    initCamera();
    //监听app进入前后台事件
    setResumeOrPauseEvent();
    //初始化动画，用于相册识别页面展示效果
    _animationController = AnimationController(
        duration: Duration(milliseconds: 1500), vsync: this);
    if (Platform.isAndroid) {
      RScan.getInstance().init().setCallBack(_doScanImgFromAndroid);
    }
  }

  void setResumeOrPauseEvent() {
    _resumeMessage =
        Message.listen<AppResumeMessage>((AppResumeMessage event) async {
      if (_controller != null && _flashMode) {
        _flashMode = false;
        _controller!.setFlashMode(_flashMode);
        _streamController?.add(_flashMode);
      }
    });

    _pauseMessage =
        Message.listen<AppPauseMessage>((AppPauseMessage event) async {
      _cameraAvailable = false;
      if (Platform.isAndroid) {
        final bool granted = await getCameraPermission();
        if (granted && _controller != null) {
          _controller!.dispose();
          _controller = null;
        }
      }
    });
  }

  Future<bool> getCameraPermission() async {
    try {
      List<String> permissions = [];
      permissions.add('camera');
      List<dynamic> resultList =
          await Uppermission.queryPermission(permissions);
      final Map<String, dynamic> resultMap =
          CommonUtil.convertType<Map<dynamic, dynamic>>(
              resultList[0], <dynamic, dynamic>{}).cast<String, dynamic>();
      final QueryPermissionResult permissionResult =
          QueryPermissionResult.fromJson(resultMap);
      return '${permissionResult.granted}' == 'true';
    } catch (e) {
      return false;
    }
  }

  void _updateCameraTipShowState(bool show) {
    LogHelper.info(
        tag: QRScanLogString.tagQRScan,
        msg: 'updateCameraTipShowState, start with show = ${show}');
    if (show == _cameraTipShow || !mounted) {
      LogHelper.info(tag: QRScanLogString.tagQRScan, msg: {
        'updateCameraTipShowState err, return with show':
            '${show == _cameraTipShow} !mounted = ${!mounted}',
        'info': '${!mounted}'
      });
      return;
    }
    setState(() {
      _cameraTipShow = show;
    });
  }

  /// 针对安卓相册图片扫码回调函数
  void _doScanImgFromAndroid(List<RScanResult>? result) {
    if (_animationController != null) {
      _animationController!.stop();
    }
    setState(() {
      _isAlbumScan = false;
    });
    if (result == null || result.isEmpty) {
      setState(() {
        _isAnimatedLine = true;
      });
      QRScanToast().showErrorText(QRScanErrorString.QR_NO_SCAN_RESULT);
    } else {
      _closeRotue(result);
    }
    if (_controller != null) {
      _controller!.startGetScanResult();
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    LogHelper.info(
        tag: QRScanLogString.tagQRScan,
        msg: 'general scan---didChangeAppLifecycleState$state.');
    switch (state) {
      case AppLifecycleState.resumed:
        onPageShow();
        break;
      case AppLifecycleState.hidden:
        onPageHide();
        break;
      case AppLifecycleState.detached:
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
        break;
    }
  }

  Future<void> onPageShow() async {
    final bool granted = await getCameraPermission();
    if (granted) {
      _updateCameraTipShowState(false);
      if (_controller == null) {
        LogHelper.info(
            tag: QRScanLogString.tagQRScan,
            msg:
                'scan onPageShow, with controller null and _cameraAvailable $_cameraAvailable.');
        if (rScanCameras == null || rScanCameras!.isEmpty) {
          _reloadScanContext();
        }
        _initCameraController();
      }
    } else {
      _updateCameraTipShowState(true);
    }
    if (_controller != null) {
      if (Platform.isIOS && _openAlbumForIOS) {
        _openAlbumForIOS = false;
        setState(() {
          _isAnimatedLine = true;
        });
      }
      if (!_isAlbumScan) {
        //没在识别相册中的图片，开始调用mPaaS
        _controller!.startGetScanResult();
      }
      final bool flashModel = (await _controller!.getFlashMode())!;
      _flashMode = flashModel;
      _streamController?.add(_flashMode);
    }
  }

  void onPageHide() {
    if (_controller != null) {
      _controller!.stopGetScanResult();
      if (_flashMode) {
        _flashMode = false;
        _controller!.setFlashMode(_flashMode);
        _streamController?.add(_flashMode);
      }
    }
  }

  void _disposeEvent() {
    _cameraAvailable = false;
    if (Platform.isAndroid) {
      if (!mounted) {
        return;
      }
      // 处理Android 扫码页面内存泄漏问题; ios 端在native侧处理
      _controller?.removeListener(() {});
      _controller?.dispose();
      _timer?.cancel();
      _streamController?.close();
      _resumeMessage?.cancel();
      _pauseMessage?.cancel();
      _animationController?.dispose();
      if (Platform.isAndroid) {
        RScan.getInstance().destroy();
      }
      WidgetsBinding.instance.removeObserver(this);
    }
  }

  @override
  void dispose() {
    super.dispose();
    _disposeEvent();
  }

  @override
  Widget build(BuildContext context) {
    QRScanCommonUtil().context = context;
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    //此处计算是根据裁剪框的大小设定的
    ScreenUtil.init(
        BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width,
            maxHeight: MediaQuery.of(context).size.height),
        designSize: width > height ? Size(240, 812) : Size(375, 812),
        orientation: Orientation.portrait);
    double boxWidth = ScreenUtil().screenWidth * (width > height ? 0.35 : 0.55);
    //加载中. 加载中.. 加载中... 循环显示 间隔500ms
    Tween(begin: 0.0, end: 1.0)
        .chain(CurveTween(curve: Interval(0, 1 / 3)))
        .animate(_animationController!);
    Tween(begin: 0.0, end: 1.0)
        .chain(CurveTween(curve: Interval(1 / 3, 2 / 3)))
        .animate(_animationController!);
    Tween(begin: 0.0, end: 1.0)
        .chain(CurveTween(curve: Interval(2 / 3, 1)))
        .animate(_animationController!);
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: <Widget>[
          _gainContainerWidget(boxWidth),
          Positioned(
            top: 90 +
                MediaQueryData.fromView(View.of(context)).padding.top +
                40 +
                MediaQueryData.fromView(View.of(context)).size.width *
                    2 /
                    3 /
                    2 -
                10,
            left: MediaQueryData.fromView(View.of(context)).size.width / 2 - 50,
            child: Offstage(
              offstage: !_isAlbumScan,
              child: AnimatedBuilder(
                animation: _animationController!,
                builder: (BuildContext context, Widget? child) {
                  return Container(
                    height: 20,
                    constraints: const BoxConstraints(minWidth: 100),
                    alignment: Alignment.center,
                    child: Text(
                      _animationController!.value <= 1 / 3
                          ? '加载中.'
                          : (_animationController!.value >= 2 / 3
                              ? '加载中...'
                              : '加载中..'),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          SingleChildScrollView(
            child: Column(
              children: <Widget>[
                Container(
                  height:
                      MediaQueryData.fromView(View.of(context)).padding.top +
                          90,
                  child: AppBarWidget(
                    title: '扫一扫', // 按照线上需求，扫一扫页面标题固定为“扫一扫”
                    showPhoto: widget.config.showDefaultIcon == 'false' &&
                        widget.config.isShowAlbum == true,
                    leftClick: _throttle(() async {
                      _backAction();
                      await Future<void>.delayed(
                          const Duration(milliseconds: 2000));
                    }),
                    rightAction: _throttle(() async {
                      if (widget.config.showDefaultIcon == 'false' &&
                          widget.config.isShowAlbum == true) {
                        _photoAction();
                        await Future<void>.delayed(
                            const Duration(milliseconds: 2000));
                      }
                    }),
                  ),
                ),
                Stack(
                  children: <Widget>[
                    SizedBox(
                      height: boxWidth + 15 + 40,
                    ),
                    if (_cameraTipShow == true)
                      Container(
                        width: boxWidth,
                        height: boxWidth,
                        margin: const EdgeInsets.only(top: 40, bottom: 15),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              margin: const EdgeInsets.only(left: 8, right: 8),
                              child: const Text(
                                  CameraPermissionConfig
                                      .QR_SCAN_CAMERA_PERMISSION_TIPS,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      fontSize: 14, color: Color(0xFFFFFFFF))),
                            ),
                            const SizedBox(
                              height: 12,
                            ),
                            GestureDetector(
                              onTap: () {
                                // 跳转设置
                                Vdn.goToPage(CameraPermissionConfig
                                    .QR_SCAN_CAMERA_PERMISSION_CONFIG_URL);
                              },
                              child: Container(
                                width: 114,
                                height: 36,
                                decoration: BoxDecoration(
                                  color: const Color(0xFF2283E2),
                                  border: Border.all(
                                      color: const Color(0xFF2283E2)),
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(18)),
                                ),
                                child: const Center(
                                  child: Text(
                                      CameraPermissionConfig
                                          .QR_SCAN_CAMERA_PERMISSION_OPEN,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: Color(0xFFFFFFFF))),
                                ),
                              ),
                            )
                          ],
                        ),
                      )
                    else
                      Container(),
                  ],
                ),
                _buildScanContentWidgets(),
                const SizedBox(
                  height: 65,
                ),
                SizedBox(
                  height: 81,
                  child: AttachmentWidget(
                    config: widget.config,
                    flashMode: _flashMode,
                    streamController: _streamController!,
                    flashButton: _buildFlashBtn,
                    takePhotoAction: _throttle(() async {
                      _photoAction();
                      await Future<void>.delayed(
                          const Duration(milliseconds: 2000));
                    }),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  // 扫码框下部说明文案
  Widget _buildScanContentWidgets() {
    final String highLightText = widget.config.highLightContent;
    final String scanContent = widget.config.scanContent ?? '';

    if (scanContent.isNotEmpty && highLightText.isEmpty) {
      return Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.only(left: 16, right: 16),
        child: Text(
          scanContent,
          textAlign: TextAlign.center,
          style: const TextStyle(
            decoration: TextDecoration.none,
            fontSize: 13,
            color: Colors.white,
          ),
        ),
      );
    }
    if (scanContent.isNotEmpty && highLightText.isNotEmpty) {
      final int hightLightIndex = scanContent.indexOf(highLightText);
      return Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.only(left: 16, right: 16),
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            text: scanContent.substring(0, hightLightIndex),
            style: const TextStyle(
              decoration: TextDecoration.none,
              fontSize: 13,
              color: Colors.white,
            ),
            children: [
              TextSpan(
                  text: highLightText,
                  style: const TextStyle(
                    decoration: TextDecoration.none,
                    fontSize: 13,
                    color: Color(0xff2283E2),
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      showDialog<dynamic>(
                          context: context,
                          barrierDismissible: true,
                          builder: (BuildContext context) {
                            return ShowHelpDialog();
                          });
                    }),
              TextSpan(
                text: scanContent
                    .substring(hightLightIndex + highLightText.length),
                style: const TextStyle(
                  decoration: TextDecoration.none,
                  fontSize: 13,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container();
  }

  Widget _buildFlashBtn(BuildContext context, AsyncSnapshot<bool> snapshot) {
    return snapshot.hasData
        ? IconButton(
            icon: snapshot.data!
                ? Image.asset(
                    'images/torch_open.png',
                    package: 'scan',
                  )
                : Image.asset(
                    'images/torch.png',
                    package: 'scan',
                  ),
            color: Colors.white,
            iconSize: 46,
            onPressed: flashButtonPressed,
          )
        : Container();
  }

  void flashButtonPressed() {
    _requestPermissionFunction('camera', isImmediate: true,
        allowedCallback: () {
      if (_controller != null) {
        _flashMode = !_flashMode;
        _controller!.setFlashMode(_flashMode);
        _streamController?.add(_flashMode);
      } else {
        // 如果权限已同意，且摄像头未初始化，此时初始化摄像头
        if (Platform.isAndroid) {
          _reloadScanContext();
        }
      }
    });
  }

  /// 函数节流
  ///
  /// [func]: 要执行的方法
  VoidCallback _throttle(
    Future Function() func,
  ) {
    bool enable = true;
    VoidCallback target = () {
      if (enable == true) {
        enable = false;
        func().then((_) {
          enable = true;
        });
      }
    };
    return target;
  }

  Widget _gainContainerWidget(double boxWidth) {
    Widget tmp;
    bool needAnimate = false;
    if (rScanCameras == null || rScanCameras!.length == 0) {
      tmp = Container(
        color: Colors.black,
        alignment: Alignment.center,
        child: Text(
          '',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),
      );
    } else if (_controller == null || !_controller!.value.isInitialized!) {
      tmp = Container(
        color: Colors.black,
      );
    } else {
      double scaleValue = 1.3;
      try {
        double sizeRatio = ScreenUtil().uiSize.aspectRatio; //0.46
        double aspectRatio = _controller!.value.aspectRatio; //0.5625
        String scale = (aspectRatio / sizeRatio).toStringAsFixed(1);
        scaleValue = double.parse(scale) + 0.1;
      } catch (e) {
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: {'catch': 'scalError', 'result': scaleValue});
      }
      tmp = Container(
        alignment: Alignment.center,
        child: Transform.scale(
          scale: scaleValue,
          child: AspectRatio(
            aspectRatio: _controller!.value.aspectRatio,
            child: RScanCamera(_controller!),
          ),
        ),
      );
      needAnimate = true;
    }
    return ScanImageView(
        child: tmp,
        needAnimate: needAnimate && !_isAlbumScan && _isAnimatedLine,
        boxWidth: boxWidth);
  }

  void _backAction() {
    _disposeEvent();
    Vdn.close(result: <String, dynamic>{'back': 'true'});
  }

  void _photoAction() {
    setState(() {
      _isAnimatedLine = false;
    });
    _openAlbum();
  }

  void _openAlbum() {
    _openAlbumForIOS = true;
    const int maxSelected = 1;
    const bool isFromScan = true;
    const bool originalImage = true;
    Vdn.goToPage('flutter://photocheck', params: <String, String>{
      'max': '$maxSelected',
      'isFromScan': '$isFromScan',
      'showOriginalBtn': '$originalImage'
    }).then((Map mapValue) async {
      Log.printLog(
          LogModle('debug', '[scan]', 'generalscan-openPhotoPicker $mapValue'));
      _openAlbumForIOS = false;
      if (mapValue['data'] is List?) {
        List? value = mapValue['data'] as List?;
        if (value != null && value.length > 0) {
          if (value[0] is! Map) {
            setState(() {
              _isAnimatedLine = true;
            });
            return;
          }
          final Map<String, dynamic> pathMap =
              CommonUtil.convertType<Map<dynamic, dynamic>>(
                  value[0], <dynamic, dynamic>{}).cast<String, dynamic>();
          final QrAlbumResultData albumResultData =
              QrAlbumResultData.fromJson(pathMap);
          final String? path = albumResultData.path;
          if (!StringUtil.isBlankString(path)) {
            _animationController!.repeat();
            setState(() {
              _isAlbumScan = true;
            });
            if (Platform.isIOS) {
              final result = await RScan.scanImagePath(path!);
              _animationController!.stop();
              setState(() {
                _isAlbumScan = false;
              });
              if (result == null) {
                setState(() {
                  _isAnimatedLine = true;
                });
                QRScanToast()
                    .showErrorText(QRScanErrorString.QR_NO_SCAN_RESULT);
              } else {
                _closeRotue(result);
              }
              if (_controller != null) {
                _controller!.startGetScanResult();
              }
            } else if (Platform.isAndroid) {
              RScan.getInstance().scanImagePathFromAndroid(path!);
            }
          } else {
            setState(() {
              _isAnimatedLine = true;
            });
          }
        } else {
          setState(() {
            _isAnimatedLine = true;
          });
        }
      }
    });
  }

  Future<void> _requestPermissionFunction(String permissionName,
      {VoidCallback? allowedCallback,
      VoidCallback? cancelCallback,
      bool isImmediate = false}) async {
    try {
      final RequestResult result = await Uppermission.requestPermission(
          [permissionName],
          immediate: isImmediate);
      if (result.isAllowed == false) {
        _updateCameraTipShowState(true);
        if (cancelCallback != null) {
          cancelCallback();
        }
      } else {
        _updateCameraTipShowState(false);
        if (allowedCallback != null) {
          allowedCallback();
        }
      }
    } catch (e) {
      _updateCameraTipShowState(false);
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': 'general__requestPermissionFunction', 'catch': e});
    }
  }

  void _reloadScanContext() async {
    rScanCameras = await availableRScanCameras();
    _initCameraController();
    //此处这么处理是为了等_controller 初始化完成
    Timer.periodic(Duration(milliseconds: 200), (t) {
      if (_controller != null) {
        _controller!.startGetScanResult();
      }
      t.cancel();
    });
  }

  Future<void> _closeRotue(List<RScanResult> result) async {
    ///延时0.3秒，为了让当前vdn能够退出
    await Future<void>.delayed(const Duration(milliseconds: 500), () {
      scanResult(result, widget.config, (Map<dynamic, dynamic> map) {
        _disposeEvent();
        Vdn.close(
            result: <String, dynamic>{'scanResult': result.first.message});
      });
    });
    setState(() {
      _isAnimatedLine = true;
    });
  }
}

class AppBarWidget extends StatelessWidget {
  final bool showPhoto;
  final String? title;
  final VoidCallback? leftClick;
  final VoidCallback? rightAction;
  AppBarWidget(
      {this.leftClick, this.title, this.rightAction, this.showPhoto = true});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 20),
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            BackButton(
              onPressed: leftClick,
              color: Colors.white,
            ),
            Text(
              (StringUtil.isBlankString(title)
                  ? QRContentString.QR_SCAN
                  : title)!,
              style: TextStyle(
                fontSize: 17,
                color: Colors.white,
              ),
            ),
            if (showPhoto)
              IconButton(
                  icon: Image.asset(
                    'images/album.png',
                    package: 'scan',
                    width: 24,
                    height: 24,
                  ),
                  color: Colors.white,
                  iconSize: 24,
                  onPressed: rightAction)
            else
              Container(width: 46),
          ]),
    );
  }
}

class AttachmentWidget extends StatefulWidget {
  @required
  final QRScanParametersConfig config;
  final StreamController<dynamic> streamController;
  final bool flashMode;
  final Widget Function(BuildContext, AsyncSnapshot<bool>) flashButton;
  final VoidCallback takePhotoAction;

  AttachmentWidget({
    required this.config,
    required this.streamController,
    required this.flashMode,
    required this.flashButton,
    required this.takePhotoAction,
  });

  _AttachmentWidgetState createState() => _AttachmentWidgetState();
}

class _AttachmentWidgetState extends State<AttachmentWidget> {
  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
        BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width,
            maxHeight: MediaQuery.of(context).size.height),
        designSize: Size(375, 812),
        orientation: Orientation.portrait);
    double centerX = ScreenUtil().screenWidth / 2;

    if (widget.config.showDefaultIcon == 'true') {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: <Widget>[
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              StreamBuilder<bool>(
                initialData: widget.flashMode,
                stream: widget.streamController.stream as Stream<bool>,
                builder: widget.flashButton,
              ),
              const Text(
                QRContentString.QR_SCAN_TORCH,
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              IconButton(
                  alignment: Alignment.center,
                  icon: Image.asset(
                    'images/new_album.png',
                    package: 'scan',
                    width: 36,
                    height: 36,
                  ),
                  color: Colors.white,
                  iconSize: 36,
                  onPressed: widget.takePhotoAction),
              const Text(
                '相册',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      );
    }

    if (!StringUtil.isBlankString(widget.config.btn1_Title) &&
        !StringUtil.isBlankString(widget.config.btn1_Link) &&
        !StringUtil.isBlankString(widget.config.btn2_Title) &&
        !StringUtil.isBlankString(widget.config.btn2_Link)) {
      return Stack(children: <Widget>[
        Positioned(
          left: 62.w,
          top: 32,
          width: 120.w,
          height: 36.w,
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(
                  Radius.circular(20.w),
                ),
                color: Colors.blue),
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                Vdn.goToPage(widget.config.btn1_Link!)
                    .then((value) {})
                    .catchError((e) {
                  LogHelper.debug(
                      tag: QRScanLogString.tagQRScan,
                      msg: {'fn': 'general_build_btn1_Link', 'catchError': e});
                });
              },
              child: Center(
                  child: Text(
                widget.config.btn1_Title ?? '',
                style: TextStyle(
                  color: Color(0xFFFFFFFF),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )),
            ),
          ),
        ),
        Positioned(
          right: 62.w,
          top: 32,
          width: 120.w,
          height: 36.w,
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(
                  Radius.circular(20.w),
                ),
                color: Colors.blue),
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                Vdn.goToPage(widget.config.btn2_Link!)
                    .then((value) {})
                    .catchError((e) {
                  LogHelper.debug(
                      tag: QRScanLogString.tagQRScan,
                      msg: {'fn': 'general_build_btn2_Link', 'catchError': e});
                });
              },
              child: Center(
                  child: Text(
                widget.config.btn2_Title ?? '',
                style: TextStyle(
                  color: Color(0xFFFFFFFF),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )),
            ),
          ),
        ),
      ]);
    } else if (!StringUtil.isBlankString(widget.config.btn1_Title) &&
        !StringUtil.isBlankString(widget.config.btn1_Link)) {
      return Stack(children: <Widget>[
        Positioned(
          left: centerX - 120 / 2,
          top: 32,
          width: 120.w,
          height: 36.w,
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(
                  Radius.circular(20.w),
                ),
                color: Colors.blue),
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                Vdn.goToPage(widget.config.btn1_Link!)
                    .then((value) {})
                    .catchError((e) {
                  LogHelper.debug(
                      tag: QRScanLogString.tagQRScan,
                      msg: {'fn': 'general_build_btn1_Link', 'catchError': e});
                });
              },
              child: Center(
                  child: Text(
                widget.config.btn1_Title ?? '',
                style: TextStyle(
                  color: Color(0xFFFFFFFF),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )),
            ),
          ),
        )
      ]);
    } else if (!StringUtil.isBlankString(widget.config.btn2_Title) &&
        !StringUtil.isBlankString(widget.config.btn2_Link)) {
      return Stack(children: <Widget>[
        Positioned(
          left: centerX - 120 / 2,
          top: 32,
          width: 120.w,
          height: 36.w,
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(
                  Radius.circular(20.w),
                ),
                color: Colors.blue),
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                Vdn.goToPage(widget.config.btn2_Link!)
                    .then((value) {})
                    .catchError((e) {
                  LogHelper.debug(
                      tag: QRScanLogString.tagQRScan,
                      msg: {'fn': 'general_build_btn2_Link', 'catchError': e});
                });
              },
              child: Center(
                  child: Text(
                widget.config.btn2_Title ?? '',
                style: TextStyle(
                  color: Color(0xFFFFFFFF),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )),
            ),
          ),
        )
      ]);
    }
    return Container();
  }
}
