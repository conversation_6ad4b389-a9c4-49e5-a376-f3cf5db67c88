import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:r_scan/r_scan.dart';
import 'package:scan/common_widget/qr_scan_toast.dart';
import 'package:scan/config/constant.dart';
import 'package:scan/config/qr_api_url.dart';
import 'package:scan/config/qr_sting.dart';
import 'package:scan/logic/storage.dart';
import 'package:scan/service/qr_scan_http_service.dart';
import 'package:scan/util/qr_log.dart';
import 'package:scan/util/qr_scan_internet_util.dart';
import 'package:scan/util/qr_scan_string_util.dart';

import 'package:vdn/vdn.dart';

import '../model/qr_scan_response_model.dart';
import '../util/common_util.dart';

typedef QRSYNScanBackFunction = void Function(Map result);

class QRSYNHandler {
  //存放短链参数的字典
  Map? _shortLinkUrlParamer;
  List? _whiteList;
  Future<void> handleScanResult(List<RScanResult>? resultList, List? whiteList,
      VoidCallback handler) async {
    LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: {
      'fn': 'parseScanStr',
      'into codestr': resultList?.map((e) => e.message)
    });
    _whiteList = whiteList;
    if (resultList == null || resultList.length == 0) {
      QRScanToast().showErrorText(QRScanErrorString.QR_NO_PARSE_PROMPT);
      return;
    }
    bool connectionStatus = await InternetUtil.checkIsReachableNetwork();
    if (!connectionStatus) {
      QRScanToast().showErrorText(QRScanErrorString.QR_NO_NETWORK_PROMPT);
      return;
    }

    int count = 0;
    for (RScanResult scanResult in resultList) {
      String str = scanResult.message!;

      if (str.contains(QRString.QR_SCAN_SHORT_LINK_MARK) ||
          str.contains(QRString.QR_SCAN_SHORT_LINK_MARK1)) {
        _parseShortLink(str);
        break;
      }
      if (str.contains(QRString.QR_SCAN_LOGIN_MARK)) {
        _parseAuthorizeLogin(str);
        handler();
        break;
      }

      if (_isLongLinkWithScanStr(str)) {
        _parseLongLink(str);
        handler();
        break;
      }
      bool _isWhite = await _isWhiteListLink(str);
      if (_isWhite) {
        _gotoPage(str, null);
        handler();
        break;
      }
      count++;
    }
    if (count == resultList.length) {
      QRScanToast().showErrorText(QRScanErrorString.QR_NO_PARSE_PROMPT);
    }
  }

  ///短链接处理
  void _parseShortLink(
    String str,
  ) async {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseShortLink', 'str': str});
    //去掉字符串中所有空格
    str = str.replaceAll(new RegExp(r"\s+\b|\b\s"), "");
    Uri u = Uri.parse(str);
    //保存参数
    _shortLinkUrlParamer = u.queryParameters;
    LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: {
      'fn': '_parseShortLink',
      '_shortLinkUrlParamer': _shortLinkUrlParamer
    });
    String shortLinkCode = _fetchShortLinkCode(str);

    //请求参数
    var paramData = {
      'sortLink': shortLinkCode,
    };
    //调用登录接口并传递参数
    var response = await QRScanHttpService.post(
        ApiUrl.QRSCAN_SYN_LONG_LINK, QRScanSignType.none,
        param: paramData);
    // post(ApiUrl.QRSCAN_LONG_LINK,param:paramData,qrs);
    if (response != null &&
        response is Map<String, dynamic> &&
        response[QRScanRequestString.QR_SCAN_REQUEST_RETCODE] ==
            QRScanRequestString.QR_SCAN_REQUEST_SUCCES) {
      final QrScanResponse shortLinkResponse =
          QrScanResponse.fromJson(response);
      dynamic data = shortLinkResponse.data;
      if (data != null && data is Map<String, dynamic>) {
        final QrScanResponseData shortLinkResData =
            QrScanResponseData.fromJson(data);
        final String longLink = shortLinkResData.longLink ?? '';
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: {'fn': '_parseShortLink', 'longLink': longLink});
        List<RScanResult> scanResult = [RScanResult(message: longLink)];
        handleScanResult(scanResult, _whiteList, () {});
      } else {
        QRScanToast().showErrorText(QRScanErrorString.QR_NO_PARSE_PROMPT);
      }
    } else {
      QRScanToast().showErrorText(QRScanErrorString.QR_NO_PARSE_PROMPT);
    }
  }

  //长链接处理
  void _parseLongLink(String str) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseLongLink', 'str': str});
    //去掉字符串中所有空格
    str = str.replaceAll(new RegExp(r"\s+\b|\b\s"), "");
    Uri u = Uri.parse(str);
    Map urlParamer = Map();
    urlParamer.addAll(u.queryParameters);
    String qrtype = CommonUtil.convertType<String>(urlParamer['qrtype'], '');
    String targeturl =
        CommonUtil.convertType<String>(urlParamer['targeturl'], '');
    if (StringUtil.isBlankString(qrtype) ||
        StringUtil.isBlankString(targeturl)) {
      QRScanToast().showErrorText(QRScanErrorString.QR_NO_PARSE_PROMPT);
      return;
    }

    urlParamer.remove('qrtype');
    urlParamer.remove('targeturl');
    if (_shortLinkUrlParamer != null) {
      urlParamer.addAll(_shortLinkUrlParamer!);
    }
    String resultUrl = _componentsUrlStr(targeturl, urlParamer);
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseLongLink', 'resultUrl': resultUrl});
    if (qrtype == 'jump') {
      _gotoPage(resultUrl, null);
    }
  }

  ///授权登录
  void _parseAuthorizeLogin(String str) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseAuthorizeLogine', 'str': str});
    List<String> list = str.split(QRString.QR_SCAN_LOGIN_MARK);
    String uuid = '';
    if (list != null && list.length > 0) {
      uuid = list.last;
    }

    if (StringUtil.isBlankString(uuid)) {
      QRScanToast().showErrorText(QRScanErrorString.QR_NO_PARSE_PROMPT);
    } else {
      //_traceEventWithVariable(QRString.QR_SCAN_LOGIN_MARK);
      //用uuid替换%@，达到字符串拼接的目的
      String loginPage = QRString.QR_SCAN_LOGIN_PAGE_URL.replaceAll('%@', uuid);
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': '_parseAuthorizeLogine', 'loginPage': loginPage});
      _gotoPage(loginPage, null);
    }
  }

  bool _isLongLinkWithScanStr(String str) {
    if (str.contains(QRString.QR_SCAN_PRODUCTION_LONG_LINK_MARK) ||
        str.contains(QRString.QR_SCAN_ACCEPTANCE_LONG_LINK_MARK)) {
      return true;
    }
    return false;
  }

  String _fetchShortLinkCode(String shortLink) {
    if (StringUtil.isBlankString(shortLink)) {
      return '';
    }
    List components = shortLink.split('?');
    if (components == null || components.length == 0) {
      return '';
    }
    String shortLinkPath = CommonUtil.convertType<String>(components.first, '');
    int index = shortLink.indexOf(QRString.QR_SCAN_SHORT_LINK_MARK);
    if (index == -1) {
      return '';
    }
    String result = shortLinkPath
        .substring(index + QRString.QR_SCAN_SHORT_LINK_MARK.length);
    return result;
  }

  String _componentsUrlStr(String str, Map param) {
    //没有参数，不需要拼接
    if (param == null || param.length == 0) {
      return str;
    }
    //如果没有?号添加?号
    int index = str.indexOf('?');
    if (index < 0) {
      str += '?';
    }

    Map paramMap = Map();
    //参数字典不为空的时候需要重拍数组
    if (index < str.length - 1) {
      //此做法默认链接中只有一个问号
      List<String> list = str.split('?');
      String paramStr;
      //原来带了参数
      if (list.length > 1) {
        str = CommonUtil.convertType<String>(list[0], '') + '?';
        paramStr = list[1];
        List paramList = paramStr.split('&');
        paramList.forEach((element) {
          String e = element as String;
          List eList = e.split('=');
          if (eList.length > 1) {
            paramMap[eList[0]] = eList[1];
          }
        });
      }
    }
    //将原来的数据放在参数的最前面
    if (paramMap.length > 0) {
      paramMap.addAll(param);
      param = paramMap;
    }
    //读取参数并组成带参数的url
    if (param != null) {
      param.forEach((key, value) {
        str += (CommonUtil.convertType<String>(key, '') +
            '=' +
            value.toString() +
            '&');
      });
      //此处多了一个&，去掉它
      str = str.substring(0, str.length - 1);
    }
    return str;
  }

  void _gotoPage(String pageUrl, Map<String, String>? parameters) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_gotoPage', 'pageUrl': pageUrl, 'parameters': parameters});
    if (parameters == null || parameters.length == 0) {
      Vdn.goToPage(pageUrl).then((value) {}).catchError((e) {
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: {'fn': '_gotoPage', 'parameters == null catchError': e});
      });
    } else {
      Vdn.goToPage(pageUrl, params: parameters)
          .then((value) {})
          .catchError((e) {
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: {'fn': '_gotoPage', 'catchError': e});
      });
    }
  }

  Future<bool> _isWhiteListLink(String str) async {
    //获取本地扫码白名单
    if (_whiteList == null || _whiteList is! List || _whiteList!.length < 1) {
      _whiteList = await QRStorage().readQrWhiteCodeData(CONSTANT.synWhiteCode);
      if (_whiteList == null || _whiteList is! List || _whiteList!.length < 1) {
        //获取本地resources数据
        String jsonStr = await rootBundle
            .loadString("packages/scan/preresource/qrwhitecodesyn.json");
        Map<String, List<dynamic>> jsonMap =
            CommonUtil.convertType<Map<dynamic, dynamic>>(
                    jsonDecode(jsonStr), <dynamic, dynamic>{})
                .cast<String, List<dynamic>>();
        _whiteList = jsonMap["whiteList"];
        return _returnIsWhiteCode(str);
      } else {
        return _returnIsWhiteCode(str);
      }
    } else {
      return _returnIsWhiteCode(str);
    }
  }

  bool _returnIsWhiteCode(String str) {
    bool _flag = false;
    for (dynamic element in _whiteList!) {
      final String? checkUrl = CommonUtil.convertType<String?>(element, null);
      if (checkUrl != null && str.contains(checkUrl)) {
        _flag = true;
        break;
      }
    }
    return _flag;
  }
}
