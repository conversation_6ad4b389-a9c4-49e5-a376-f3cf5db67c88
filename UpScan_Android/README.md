# UpScan Android 项目

基于QBarSDK实现的原生Android扫码库，严格按照Flutter中原有逻辑处理扫码结果。

## 项目架构

### 扫码业务层 (Business Layer)
- **pages** - 扫码页面类，包含具体的扫码页面实现
- **handlers** - Handler类，处理不同类型的扫码结果逻辑

### 基础扫码能力层 (Core Scanning Layer)  
- **core/pages** - 基础扫码UI组件和相机扫码功能
- **core/qbarsdk** - 腾讯扫码SDK的集成和封装
- **core/common** - 共有逻辑部分公共类，通用扫码逻辑、工具类等
- **core/handlers** - Handler基类，Handler的基础抽象类

## 目录结构

```
UpScan_Android/
├── Docs/                           # 项目文档
├── src/main/java/com/uplus/upscan/
│   ├── business/                   # 扫码业务层
│   │   ├── pages/                  # 扫码页面类
│   │   └── handlers/               # 业务Handler类
│   └── core/                       # 基础扫码能力层
│       ├── pages/                  # 基础扫码页面类
│       ├── qbarsdk/               # 腾讯扫码SDK集成
│       ├── common/                # 共有逻辑公共类
│       └── handlers/              # Handler基类
└── README.md
```

## 开发说明

本项目严格按照Flutter扫码库的逻辑进行Android原生实现，确保扫码结果处理的一致性。
