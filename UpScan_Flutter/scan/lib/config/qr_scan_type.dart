enum QRScanType {
  // 【扫码购买】解析成功
  QRScanTypeBuyJump,

  // 【扫码授权登陆】解析成功
  QRScanTypeAuthorizeLogin,

  // 【扫码加入家庭】解析成功
  QRScanTypeJoinFamily,

  // 【扫码绑定】解析成功
  QRScanTypeBuyBinding,

  //虚拟设备
  QRScanTypeVirtualDev,

  // 扫码解析失败
  QRScanTypeParseError,

  // 网络异常
  QRScanTypeNoNetwork,

  // 解析成功，other
  QRScanTypeOther,
  //迁移任务跳转
  QRSecurity,
  //迁移任务数据解析异常
  QRSecurityInfoError,
}

enum QRScanSourceType {
  QRScanSourceTypeCamera,
  QRScanSourceTypeAlbum,
  QRScanSourceTypeOther
}
