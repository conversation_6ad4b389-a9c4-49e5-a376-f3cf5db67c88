package com.uplus.upscan.core.handlers;

import android.app.Activity;
import java.lang.ref.WeakReference;

/**
 * 扫码Handler基类
 * 实现责任链模式，用于处理不同类型的扫码结果
 * 包含弱引用管理和Activity生命周期检查，避免内存泄漏
 */
public abstract class UpScanBaseHandler {

    /**
     * 扫码控制接口的弱引用，避免内存泄漏
     */
    protected WeakReference<UpScanControlDelegate> upScanControlDelegate;

    /**
     * 责任链中的下一个Handler
     */
    private UpScanBaseHandler nextHandler;

    /**
     * 设置扫码控制接口
     * @param controlDelegate 扫码控制接口实例
     */
    public void setUpScanControlDelegate(UpScanControlDelegate controlDelegate) {
        this.upScanControlDelegate = new WeakReference<>(controlDelegate);
    }

    /**
     * 获取扫码控制接口
     * 包含Activity生命周期检查，确保Activity未销毁
     * @return 扫码控制接口实例，如果Activity已销毁则返回null
     */
    protected UpScanControlDelegate getControlDelegate() {
        if (upScanControlDelegate == null) {
            return null;
        }

        UpScanControlDelegate controlDelegate = upScanControlDelegate.get();
        if (controlDelegate == null) {
            return null;
        }

        // 检查Activity是否已销毁
        if (controlDelegate instanceof Activity) {
            Activity activity = (Activity) controlDelegate;
            if (activity.isFinishing() || activity.isDestroyed()) {
                return null;
            }
        }

        return controlDelegate;
    }

    /**
     * 判断当前Handler是否能处理该扫码结果
     * 子类需要重写此方法，实现具体的判断逻辑
     * @param code 扫码结果字符串
     * @return true表示能处理，false表示不能处理
     */
    public abstract boolean canHandle(String code);

    /**
     * 处理扫码结果的具体逻辑
     * 子类需要重写此方法，实现具体的处理逻辑
     * @param code 扫码结果字符串
     * @param source 扫码结果来源
     */
    public abstract void doHandle(String code, UpScanResultSource source);

    /**
     * 设置责任链中的下一个Handler
     * @param handler 下一个Handler
     */
    public void setNextHandler(UpScanBaseHandler handler) {
        this.nextHandler = handler;
    }

    /**
     * 获取责任链中的下一个Handler
     * @return 下一个Handler
     */
    protected UpScanBaseHandler getNextHandler() {
        return nextHandler;
    }

    /**
     * 责任链处理入口
     * 如果当前Handler能处理则直接处理，否则传递给下一个Handler
     * @param code 扫码结果字符串
     * @param source 扫码结果来源
     */
    public void handle(String code, UpScanResultSource source) {
        if (canHandle(code)) {
            doHandle(code, source);
            return;
        }

        if (nextHandler != null) {
            nextHandler.handle(code, source);
        }
    }

    /**
     * 暂停扫码
     * 通过控制接口暂停扫码功能
     */
    protected void pauseScan() {
        UpScanControlDelegate delegate = getControlDelegate();
        if (delegate != null) {
            delegate.pauseScan();
        }
    }

    /**
     * 恢复扫码
     * 通过控制接口恢复扫码功能
     */
    protected void resumeScan() {
        UpScanControlDelegate delegate = getControlDelegate();
        if (delegate != null) {
            delegate.resumeScan();
        }
    }
}