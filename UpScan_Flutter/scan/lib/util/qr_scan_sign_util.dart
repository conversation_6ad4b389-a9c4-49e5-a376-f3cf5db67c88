import 'dart:convert';
import 'package:convert/convert.dart';
import 'package:crypto/crypto.dart';

class QRScanSignUtil {
  /// sha256 加密
  // ignore: non_constant_identifier_names
  static String generate_sha256 (String data) {
    var content = new Utf8Encoder().convert(data);
    var digest = sha256.convert(content);
    return hex.encode(digest.bytes);
  }

  /// md5 加密
  // ignore: non_constant_identifier_names
  static String generate_md5 (String data) {
    var content = new Utf8Encoder().convert(data);
    var digest = md5.convert(content);
    return hex.encode(digest.bytes);
  }
}