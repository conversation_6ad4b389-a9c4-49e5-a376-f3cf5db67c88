# UpScan Android 目录结构说明

## 完整目录结构

```
UpScan_Android/
├── Docs/                                                    # 项目文档目录
│   ├── 架构设计文档.md                                        # 架构设计文档
│   └── 目录结构说明.md                                        # 本文件
├── src/main/
│   ├── java/com/uplus/upscan/
│   │   ├── business/                                        # 扫码业务层
│   │   │   ├── pages/                                       # 业务扫码页面
│   │   │   │   ├── camera/                                  # 相机扫码页面
│   │   │   │   │   ├── QRCameraScanActivity.java           # 主相机扫码页面
│   │   │   │   │   └── QRSynCameraScanActivity.java        # 同步相机扫码页面
│   │   │   │   └── general/                                 # 通用扫码页面
│   │   │   │       └── QRGeneralScanActivity.java          # 通用扫码页面
│   │   │   └── handlers/                                    # 业务Handler
│   │   │       ├── string/                                  # 字符串处理Handler
│   │   │       │   └── QRScanStringHandler.java            # 扫码字符串处理
│   │   │       └── gotopage/                               # 页面跳转Handler
│   │   │           └── QRScanGotoPageHandler.java          # 页面跳转处理
│   │   └── core/                                           # 基础扫码能力层
│   │       ├── pages/                                      # 基础扫码页面
│   │       │   ├── BaseScanActivity.java                   # 扫码Activity基类
│   │       │   └── ScanResultCallback.java                 # 扫码结果回调接口
│   │       ├── qbarsdk/                                    # QBarSDK集成
│   │       │   ├── QBarScanManager.java                    # QBarSDK管理类
│   │       │   └── wrapper/                                # SDK封装类
│   │       │       ├── QBarWrapper.java                    # QBar封装类
│   │       │       └── ScanResult.java                     # 扫码结果封装
│   │       ├── common/                                     # 公共组件
│   │       │   ├── config/                                 # 配置类
│   │       │   │   ├── QRScanType.java                     # 扫码类型定义
│   │       │   │   ├── QRString.java                       # 字符串常量
│   │       │   │   └── QRScanConfig.java                   # 扫码配置
│   │       │   ├── model/                                  # 数据模型
│   │       │   │   ├── QRScanResult.java                   # 扫码结果模型
│   │       │   │   ├── QRScanResponse.java                 # 扫码响应模型
│   │       │   │   └── QRScanSourceType.java               # 扫码源类型
│   │       │   ├── util/                                   # 工具类
│   │       │   │   ├── QRScanUtil.java                     # 扫码工具类
│   │       │   │   ├── CommonUtil.java                     # 通用工具类
│   │       │   │   └── QRLogUtil.java                      # 日志工具类
│   │       │   └── service/                                # 服务类
│   │       │       ├── QRScanHttpService.java              # HTTP服务
│   │       │       └── QRScanNetworkService.java           # 网络服务
│   │       └── handlers/                                   # Handler基类
│   │           ├── BaseHandler.java                        # Handler基类
│   │           └── ScanResultHandler.java                  # 扫码结果处理接口
│   └── res/                                                # 资源文件
│       ├── layout/                                         # 布局文件
│       │   ├── activity_qr_camera_scan.xml                 # 相机扫码布局
│       │   └── activity_qr_general_scan.xml                # 通用扫码布局
│       ├── values/                                         # 值资源
│       │   ├── strings.xml                                 # 字符串资源
│       │   ├── colors.xml                                  # 颜色资源
│       │   └── dimens.xml                                  # 尺寸资源
│       └── drawable/                                       # 图片资源
│           ├── ic_scan_frame.xml                           # 扫码框图标
│           └── ic_flash_light.xml                          # 闪光灯图标
└── README.md                                               # 项目说明文档
```

## 目录说明

### 1. 扫码业务层 (business/)

#### 1.1 pages/camera/
- **QRCameraScanActivity.java**: 主要的相机扫码页面，对应Flutter的`qr_camera_scan.dart`
- **QRSynCameraScanActivity.java**: 同步相机扫码页面，对应Flutter的`qr_syn_camera_scan.dart`

#### 1.2 pages/general/
- **QRGeneralScanActivity.java**: 通用扫码页面，对应Flutter的`qr_general_scan.dart`

#### 1.3 handlers/string/
- **QRScanStringHandler.java**: 扫码字符串处理器，对应Flutter的`qr_scan_string_handler.dart`
  - 解析扫码结果字符串
  - 判断扫码类型（绑定码、短链、长链等）
  - 处理不同类型的扫码逻辑

#### 1.4 handlers/gotopage/
- **QRScanGotoPageHandler.java**: 页面跳转处理器，对应Flutter的`qr_scan_goto_page_handler.dart`
  - 根据扫码结果进行页面分类跳转
  - 处理不同业务场景的页面导航

### 2. 基础扫码能力层 (core/)

#### 2.1 pages/
- **BaseScanActivity.java**: 扫码Activity的基类，提供通用的扫码功能
- **ScanResultCallback.java**: 扫码结果回调接口定义

#### 2.2 qbarsdk/
- **QBarScanManager.java**: QBarSDK的管理类，负责SDK的初始化和配置
- **wrapper/QBarWrapper.java**: QBarSDK的封装类，提供统一的扫码接口
- **wrapper/ScanResult.java**: 扫码结果的封装类

#### 2.3 common/config/
- **QRScanType.java**: 扫码类型枚举，对应Flutter的`qr_scan_type.dart`
- **QRString.java**: 字符串常量定义，对应Flutter的`qr_sting.dart`
- **QRScanConfig.java**: 扫码配置类，对应Flutter的`qr_general_scan_config.dart`

#### 2.4 common/model/
- **QRScanResult.java**: 扫码结果数据模型
- **QRScanResponse.java**: 扫码响应数据模型
- **QRScanSourceType.java**: 扫码源类型枚举

#### 2.5 common/util/
- **QRScanUtil.java**: 扫码相关工具类
- **CommonUtil.java**: 通用工具类，对应Flutter的`common_util.dart`
- **QRLogUtil.java**: 日志工具类，对应Flutter的`qr_log.dart`

#### 2.6 common/service/
- **QRScanHttpService.java**: HTTP服务类，对应Flutter的`qr_scan_http_service.dart`
- **QRScanNetworkService.java**: 网络服务类

#### 2.7 handlers/
- **BaseHandler.java**: Handler的基类，定义通用的处理接口
- **ScanResultHandler.java**: 扫码结果处理接口

### 3. 资源文件 (res/)

#### 3.1 layout/
- 包含各种Activity的布局文件

#### 3.2 values/
- 包含字符串、颜色、尺寸等资源定义

#### 3.3 drawable/
- 包含扫码相关的图标和图片资源

## 与Flutter对应关系

| Android路径 | Flutter路径 | 说明 |
|------------|-------------|------|
| business/handlers/string/ | handler/qr_scan_string_handler.dart | 字符串处理逻辑 |
| business/handlers/gotopage/ | handler/qr_scan_goto_page_handler.dart | 页面跳转逻辑 |
| business/pages/camera/ | pages/qr_camera_scan.dart | 相机扫码页面 |
| business/pages/general/ | pages/qr_general_scan.dart | 通用扫码页面 |
| core/common/config/ | config/ | 配置和常量 |
| core/common/model/ | model/ | 数据模型 |
| core/common/util/ | util/ | 工具类 |
| core/common/service/ | service/ | 服务类 |

这样的目录结构确保了Android项目与Flutter项目的逻辑对应关系，便于后续的开发和维护。
