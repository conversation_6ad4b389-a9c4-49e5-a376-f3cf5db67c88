import 'dart:convert';
import 'dart:io';

import 'package:Appinfos/Appinfos.dart';
import 'package:Appinfos/AppinfosModel.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:r_scan/r_scan.dart';
import 'package:scan/config/constant.dart';
import 'package:scan/config/qr_api_url.dart';
import 'package:scan/config/qr_scan_type.dart';
import 'package:scan/config/qr_sting.dart';
import 'package:scan/logic/storage.dart';
import 'package:scan/service/qr_scan_http_service.dart';
import 'package:scan/util/qr_log.dart';
import 'package:scan/util/qr_scan_common_util.dart';
import 'package:scan/util/qr_scan_internet_util.dart';
import 'package:scan/util/qr_scan_string_util.dart';
import 'package:trace/trace.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/modle/user_info_model.dart';
import 'package:user/user.dart';

import '../common_widget/qr_scan_bottom_sheet_dialog.dart';
import '../common_widget/qr_scan_join_family_loading.dart';
import '../model/qr_scan_invite_family_model.dart';
import '../model/qr_scan_response_model.dart';
import '../service/request_env.dart';
import '../service/server.dart';
import '../util/common_util.dart';
import '../util/device_sign.dart';

typedef CompleteHandler = void Function(
    String result, QRScanType type, String originalStr);

RScanCameraController? rScanController;

class QRScanStringHandler {
  //存放短链参数的字典
  Map? _shortLinkUrlParamer;
  QRScanSourceType? _sourcetype;
  int _milliseconds = 0;
  int? _brightness = 0;
  List? _whiteList;

  bool _needStartScan = true;

  Future<void> parseScanStr(
      List<RScanResult>? resultList,
      List? whiteList,
      QRScanSourceType? sourcetype,
      int milliseconds,
      CompleteHandler handler) async {
    _sourcetype = sourcetype;
    _milliseconds = milliseconds;
    _whiteList = whiteList;
    LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: {
      'fn': 'parseScanStr',
      'into codestr': resultList?.map((e) => e.message)
    });
    if (resultList == null || resultList.length == 0) {
      handler(QRScanErrorString.QR_NO_PARSE_PROMPT,
          QRScanType.QRScanTypeParseError, '');
      return;
    }

    int count = 0;
    for (RScanResult scanResult in resultList) {
      String? str = scanResult.message;
      if (str == null) {
        count++;
        continue;
      }
      if (str.startsWith(QRString.QR_SCAN_SECURITY_CODE_MARK)) {
        _parseSecurity(str, handler);
        break;
      }
      if (str.contains(QRString.QR_SCAN_BCOID_CODE_MARK) ||
          str.contains(QRString.QR_SCAN_BCENERGY_EFFICIENCY_CODE_MARK)) {
        _brightness = scanResult.brightness;
        _parseBindingLink(str, handler);
        break;
      }

      if (str.contains('SN:') && str.contains('MAC:')) {
        // 新增支持（有线+无线）摄像头二维码
        _brightness = scanResult.brightness;
        String barcode = '';
        String deviceId = '';
        final List<String> strList = str.split(';');
        for (final String element in strList) {
          if (element.startsWith('SN:')) {
            barcode = element.replaceFirst('SN:', '');
          } else if (element.startsWith('MAC:')) {
            deviceId = element.replaceFirst('MAC:', '');
          }
        }
        _parseBindingLinkForBLECamera(str, handler, barcode, deviceId);
        break;
      }

      if (str.contains(QRString.QR_SCAN_SHORT_LINK_MARK)) {
        _parseShortLink(str, handler);
        break;
      }

      //新加逻辑-适配zj9-扫地机器人
      if (str.contains(CommonUtil.getServerEnv() == ServerEnv.YANSHOU
          ? QRString.QR_SCAN_YANSHOU_SHORT_LINK_MARK1
          : QRString.QR_SCAN_SHORT_LINK_MARK1)) {
        _parseZj9ShortLink(str, handler);
        break;
      }

      if (_isVirtualDevStr(str)) {
        _brightness = scanResult.brightness;
        _parseVirtualDev(str, handler);
        break;
      }

      if (str.contains(QRString.QR_SCAN_LOGIN_MARK)) {
        _brightness = scanResult.brightness;
        _parseAuthorizeLogine(str, handler);
        break;
      }

      if (_isLongLinkWithScanStr(str)) {
        _brightness = scanResult.brightness;
        _parseLongLink(str, handler);
        break;
      }

      if (str.contains(QRString.QR_SCAN_ADD_FANILY_MARK)) {
        _brightness = scanResult.brightness;
        _dealScanToJoinFamilyLogic(str, handler);
        break;
      }
      // 屏蔽识别校园洗二维码逻辑
      // if (str.contains(QRString.QR_SCAN_WASHCALL_MARK) ||
      //     str.contains(QRString.QR_SCAN_WASHCALL_OTHER_MARK)) {
      //   _brightness = scanResult.brightness;
      //   _parseWashCallLink(str, handler);
      //   break;
      // }

      str = str.trim();
      if (str.length == 20 || str.length == 22) {
        _brightness = scanResult.brightness;
        _parseBindingLink(str, handler);
        break;
      }

      bool _isWhite = await _isWhiteListLink(str);
      if (_isWhite) {
        _dealWhiteCodeLink(str, handler);
        break;
      }
      count++;
    }
    if (count == resultList.length) {
      _traceEventWithVariable(_nonBusinessCodeStr(resultList));
      handler(QRScanErrorString.QR_NO_PARSE_PROMPT,
          QRScanType.QRScanTypeParseError, _nonBusinessCodeStr(resultList));
    }
  }

  Future<bool> _isWhiteListLink(String str) async {
    //获取本地扫码白名单
    if (_whiteList == null || _whiteList is! List || _whiteList!.length < 1) {
      _whiteList =
          await QRStorage().readQrWhiteCodeData(CONSTANT.zhijiaWhiteCode);
      if (_whiteList == null || _whiteList is! List || _whiteList!.length < 1) {
        //获取本地resources数据
        String jsonStr = await rootBundle
            .loadString("packages/scan/preresource/qrwhitecode.json");
        final Map<String, List<dynamic>> jsonMap =
            CommonUtil.convertType<Map<dynamic, dynamic>>(
                jsonDecode(jsonStr), {}).cast<String, List<dynamic>>();
        _whiteList = jsonMap["whiteList"];
        return _returnIsWhiteCode(str);
      } else {
        return _returnIsWhiteCode(str);
      }
    } else {
      return _returnIsWhiteCode(str);
    }
  }

  bool _returnIsWhiteCode(String str) {
    bool _flag = false;
    for (dynamic element in _whiteList!) {
      final String? checkUrl = CommonUtil.convertType<String?>(element, null);
      if (checkUrl != null && str.contains(checkUrl)) {
        _flag = true;
        break;
      }
    }
    return _flag;
  }

  //获取非业务码的结果合集
  String _nonBusinessCodeStr(List<RScanResult> resultList) {
    String _str = '';
    if (resultList == null || resultList.length == 0) {
      return _str;
    }
    //取第一个结果的灰度值
    _brightness = resultList[0].brightness;
    resultList.forEach((element) {
      _str = _str + ',' + element.message!;
    });
    return _str;
  }

  Future<void> _dealWhiteCodeLink(String str, CompleteHandler complete) async {
    bool connectionStatus = await InternetUtil.checkIsReachableNetwork();
    if (!connectionStatus) {
      complete(QRScanErrorString.QR_NO_NETWORK_PROMPT,
          QRScanType.QRScanTypeNoNetwork, str);
      return;
    }
    complete(str, QRScanType.QRScanTypeBuyJump, str);
  }

  ///扫码绑定
  Future<void> _parseBindingLink(String str, CompleteHandler complete) async {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseBindingLink', 'str': str});
    LoginStatus status = await User.getLoginStatus();
    if (status.isLogin != true) {
      complete(QRScanErrorString.QR_NOT_LOGIN_PROMPT,
          QRScanType.QRScanTypeParseError, str);
      return;
    }
    _traceEventWithVariable(QRString.QR_SCAN_BCOID_CODE_MARK);
    LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, String>{
      'fn': '_parseBindingLink',
      'reslut': QRString.QR_SCAN_BINDING_PAGE_URL
    });

    /// 判断是否跳转Native绑定页面
    final bool isH5BindingPage = await CommonUtil.getABTestDataForBind();
    if (!isH5BindingPage) {
      complete(QRString.QR_SCAN_BINDING_PAGE_URL,
          QRScanType.QRScanTypeBuyBinding, str);
    } else {
      String res = await _fetchDeviceModeInfo(str);
      if (res.isEmpty) {
        complete(QRScanErrorString.QR_NO_NETWORK_PROMPT,
            QRScanType.QRScanTypeNoNetwork, str);
      } else {
        String bindPage = QRString.QR_SCAN_BINDING_PAGE_URL_FOR_H5;
        res = base64Url.encode(utf8.encode(res));
        bindPage = '$bindPage&codeType=AppTypeCode&scanresult=$res';

        LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: {
          'fn': '_parseBindingLink',
          'result': bindPage,
        });

        complete(bindPage, QRScanType.QRScanTypeBuyJump, str);
      }
    }
  }

  ///扫码绑定
  ///扫机身码/BLE绑定(有线+无线)摄像头
  Future<void> _parseBindingLinkForBLECamera(
    String str,
    CompleteHandler complete,
    String sn,
    String mac,
  ) async {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: <String, String>{'fn': '_parseBindingLink', 'str': str});
    final LoginStatus status = await User.getLoginStatus();
    if (!status.isLogin) {
      complete(QRScanErrorString.QR_NOT_LOGIN_PROMPT,
          QRScanType.QRScanTypeParseError, str);
      return;
    }
    _traceEventWithVariable(QRString.QR_SCAN_BCOID_CODE_MARK);
    LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, String>{
      'fn': '_parseBindingLink',
      'reslut': QRString.QR_SCAN_BINDING_PAGE_URL
    });

    /// 判断是否跳转Native绑定页面
    final bool isH5BindingPage = await CommonUtil.getABTestDataForBind();
    if (!isH5BindingPage) {
      complete(QRString.QR_SCAN_BINDING_PAGE_URL,
          QRScanType.QRScanTypeBuyBinding, str);
    } else {
      String bindPage = QRString.QR_SCAN_BINDING_PAGE_URL_FOR_H5;
      try {
        LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, dynamic>{
          'fn': '_parseBindingLink',
          'sn': sn,
          'mac': mac,
        });
        String res = await _fetchDeviceModeInfo(sn);
        if (res.isEmpty) {
          complete(QRScanErrorString.QR_NO_NETWORK_PROMPT,
              QRScanType.QRScanTypeNoNetwork, str);
        } else {
          final Map<String, dynamic> temp =
              CommonUtil.convertType<Map<String, dynamic>>(
                  json.decode(res), <String, dynamic>{});
          final Map<String, dynamic> data =
              CommonUtil.convertType<Map<String, dynamic>>(
                  temp['data'], <String, dynamic>{});
          data['deviceId'] = mac;
          res = json.encode(temp);
          res = base64Url.encode(utf8.encode(res));
          bindPage = '$bindPage&codeType=AppTypeCode&scanresult=$res';

          LogHelper.debug(
              tag: QRScanLogString.tagQRScan,
              msg: <String, dynamic>{
                'fn': '_parseBindingLink',
                'result': bindPage,
              });

          complete(bindPage, QRScanType.QRScanTypeBuyJump, str);
        }
      } catch (e) {
        LogHelper.error(tag: QRScanLogString.tagQRScan, msg: <String, dynamic>{
          'fn': '_parseBindingLink',
          'result': e,
        });
        complete(bindPage, QRScanType.QRScanTypeBuyJump, str);
      }
    }
  }

  //处理zj9短链
  Future<void> _parseZj9ShortLink(String str, CompleteHandler complete) async {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseZj9ShortLink', 'str': str});
    //去掉字符串中所有空格
    str = str.replaceAll(new RegExp(r"\s+\b|\b\s"), "");
    Uri u = Uri.parse(str);
    Map urlParamer = Map();
    urlParamer.addAll(u.queryParameters);
    final String? _p = CommonUtil.convertType<String?>(urlParamer['_p'], null);
    if (_p != null && StringUtil.isBlankString(_p) || _p != 'jp') {
      complete(QRScanErrorString.QR_NO_PARSE_PROMPT,
          QRScanType.QRScanTypeParseError, str);
      return;
    } else {
      //网络不可用提示
      bool connectionStatus = await InternetUtil.checkIsReachableNetwork();
      if (!connectionStatus) {
        complete(QRScanErrorString.QR_NO_NETWORK_ERROR,
            QRScanType.QRScanTypeNoNetwork, str);
        return;
      }
      //获取跳转链接
      //请求参数
      var paramData = {
        'scanUrl': str,
      };
      //调用登录接口并传递参数
      var response = await QRScanHttpService.post(
          RequestUtil.getSerEnv() + '${ApiUrl.QR_SCAN_GET_LONG_PAGE_URL}',
          QRScanSignType.none,
          param: paramData);
      if (response != null && response is Map<String, dynamic>) {
        final QrScanResponse zj9shortLinkResponse =
            QrScanResponse.fromJson(response);
        if (zj9shortLinkResponse.retCode ==
            QRScanRequestString.QR_SCAN_REQUEST_SUCCES) {
          QrScanResponseData? qrScanShortLinkData;
          dynamic json = zj9shortLinkResponse.data;
          if (json is Map<String, dynamic>) {
            qrScanShortLinkData = QrScanResponseData.fromJson(json);
          }
          if (qrScanShortLinkData is QrScanResponseData) {
            String jumpUrl = qrScanShortLinkData.jumpUrl ?? '';
            //添加打点
            _traceEventWithVariable(
                CommonUtil.getServerEnv() == ServerEnv.YANSHOU
                    ? QRString.QR_SCAN_YANSHOU_SHORT_LINK_MARK1
                    : QRString.QR_SCAN_SHORT_LINK_MARK1);
            LogHelper.debug(
                tag: QRScanLogString.tagQRScan,
                msg: {'fn': '_parseZj9ShortLink', 'jumpUrl': jumpUrl});

            //判断是否为跳转Native绑定页面
            final bool isH5BindingPage =
                await CommonUtil.getABTestDataForBind();
            if (!isH5BindingPage) {
              complete(jumpUrl, QRScanType.QRScanTypeBuyJump, str);
            } else {
              // 绑定业务迁移 - 处理跳转链接、请求接口获取scanCode
              String res = await _fetchDeviceModeInfo(jumpUrl);
              if (res.isEmpty) {
                complete(QRScanErrorString.QR_NO_NETWORK_PROMPT,
                    QRScanType.QRScanTypeNoNetwork, str);
              } else {
                String bindPage = QRString.QR_SCAN_BINDING_PAGE_URL_FOR_H5;
                res = base64Url.encode(utf8.encode(res));
                bindPage = '$bindPage&codeType=AppTypeCode&scanresult=$res';

                complete(bindPage, QRScanType.QRScanTypeBuyJump, str);
              }
            }
          } else {
            complete(QRScanErrorString.QR_NO_NETWORK_PROMPT,
                QRScanType.QRScanTypeNoNetwork, str);
          }
        } else {
          complete(QRScanErrorString.QR_NO_NETWORK_PROMPT,
              QRScanType.QRScanTypeNoNetwork, str);
        }
      } else {
        complete(QRScanErrorString.QR_NO_NETWORK_PROMPT,
            QRScanType.QRScanTypeNoNetwork, str);
      }
    }
  }

  // 扫码查询设备信息v3(增加查询未接入海极网逻辑)
  // jumpUrl: https://uplus.haier.com/uplusapp/bind/scanbindentrance.html?scanCode=F551A1000&code_type=download
  // jumpUrl: http://el.bbqk.com/wbqan/0.html --- oid码/能效码/条形码
  // http://oid.haier.com/oid?ewm=D006MAN$LvNFTKB$BN$N$cN$KN$FYN$FB$N$KBKB$LN$$K$F$vKKN$KvcNBKB$K=KTNKKKFF$0
  Future<String> _fetchDeviceModeInfo(String jumpUrl) async {
    try {
      LogHelper.info(tag: QRScanLogString.tagQRScan, msg: <String, String>{
        'fn': '_fetchDeviceModeInfo',
        'info': 'jumpUrl is $jumpUrl'
      });
      // 请求参数, 取出scanCode、code_type，判断code_type是否为download,
      // 是的话，需要在scanCode后拼接11个1
      // 否则直接将scanCode传入接口
      final List<String> urlList = jumpUrl.split('?');
      String codeType = '';
      String scanCode = '';
      if (urlList.length > 1 &&
          jumpUrl.contains('scanCode=') &&
          jumpUrl.contains('code_type=')) {
        final String queryParams = urlList[1];
        final List<String> queryParamList = queryParams.split('&');
        for (final String queryParam in queryParamList) {
          final List<String> queryParamItemList = queryParam.split('=');
          if (queryParamItemList.length > 1) {
            final String key = queryParamItemList[0];
            final String value = queryParamItemList[1];
            if (key == 'code_type') {
              codeType = value;
            } else if (key == 'scanCode') {
              scanCode = value;
            }
          }
        }

        if (codeType == 'download') {
          scanCode = '${scanCode}11111111111';
        }
      } else {
        // 无查询参数
        scanCode = jumpUrl;
      }
      LogHelper.info(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': '_fetchDeviceModeInfo', 'info': 'scanCode is $scanCode'});
      final Map<String, dynamic> paramData = <String, dynamic>{
        'code': scanCode,
        'isRtf': 'true',
      };
      final Map<String, dynamic>? response = await QRScanHttpService.postNew(
          RequestUtil.getSerEnv(),
          ApiUrl.QR_SCAN_GET_DEVICE_MODE_INFO,
          QRScanSignType.sha256_for_zj,
          param: paramData);
      LogHelper.info(tag: QRScanLogString.tagQRScan, msg: <String, String>{
        'fn': '_fetchDeviceModeInfo',
        'info': 'response is $response'
      });
      if (response != null) {
        return json.encode(response);
      }
      return '';
    } catch (e) {
      LogHelper.error(
          tag: QRScanLogString.tagQRScan,
          msg: <String, Object>{'fn': '_fetchDeviceModeInfo', 'err': e});
      return '';
    }
  }

  ///短链接处理
  void _parseShortLink(String str, CompleteHandler complete) async {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseShortLink', 'str': str});
    //去掉字符串中所有空格
    str = str.replaceAll(new RegExp(r"\s+\b|\b\s"), "");
    Uri u = Uri.parse(str);
    //保存参数
    _shortLinkUrlParamer = u.queryParameters;
    LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: {
      'fn': '_parseShortLink',
      '_shortLinkUrlParamer': _shortLinkUrlParamer
    });
    String shortLinkCode = _fetchShortLinkCode(str);

    //请求参数
    var paramData = {
      'sortLink': shortLinkCode,
    };
    //调用登录接口并传递参数
    var response = await QRScanHttpService.post(
        ApiUrl.QRSCAN_LONG_LINK, QRScanSignType.none,
        param: paramData);
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseShortLink', 'response': response});
    if (response != null &&
        response is Map &&
        response[QRScanRequestString.QR_SCAN_REQUEST_RETCODE] ==
            QRScanRequestString.QR_SCAN_REQUEST_SUCCES) {
      final QrScanResponse shortLinkResponse =
          QrScanResponse.fromJson(response);
      QrScanResponseData? shortLinkData;
      if (shortLinkResponse != null && shortLinkResponse.data is Map) {
        final Map<String, dynamic> dataJson =
            CommonUtil.convertType<Map<dynamic, dynamic>>(
                    shortLinkResponse.data, <dynamic, dynamic>{})
                .cast<String, dynamic>();
        shortLinkData = QrScanResponseData.fromJson(dataJson);
      }
      if (shortLinkData is QrScanResponseData) {
        String longLink = shortLinkData.longLink ?? '';
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: {'fn': '_parseShortLink', 'longLink': longLink});
        List<RScanResult> scanResult = [RScanResult(message: longLink)];
        parseScanStr(
            scanResult, _whiteList, _sourcetype, _milliseconds, complete);
      } else {
        complete(QRScanErrorString.QR_NO_PARSE_PROMPT,
            QRScanType.QRScanTypeNoNetwork, str);
      }
    } else {
      complete(QRScanErrorString.QR_NO_PARSE_PROMPT,
          QRScanType.QRScanTypeNoNetwork, str);
    }
  }

  //长链接处理
  void _parseLongLink(String str, CompleteHandler complete) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseLongLink', 'str': str});
    //去掉字符串中所有空格
    str = str.replaceAll(new RegExp(r"\s+\b|\b\s"), "");
    Uri u = Uri.parse(str);
    Map urlParamer = Map();
    urlParamer.addAll(u.queryParameters);
    String qrtype = CommonUtil.convertType<String>(urlParamer['qrtype'], '');
    String targeturl =
        CommonUtil.convertType<String>(urlParamer['targeturl'], '');
    if (StringUtil.isBlankString(qrtype) ||
        StringUtil.isBlankString(targeturl)) {
      complete(QRScanErrorString.QR_NO_PARSE_PROMPT,
          QRScanType.QRScanTypeParseError, str);
      return;
    }

    urlParamer.remove('qrtype');
    urlParamer.remove('targeturl');
    if (_shortLinkUrlParamer != null) {
      urlParamer.addAll(_shortLinkUrlParamer!);
    }
    String resultUrl = _componentsUrlStr(targeturl, urlParamer);
    _traceEventWithVariable(QRString.QR_SCAN_PRODUCTION_LONG_LINK_MARK);
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseLongLink', 'resultUrl': resultUrl});
    complete(
        resultUrl,
        qrtype == 'jump'
            ? QRScanType.QRScanTypeBuyJump
            : QRScanType.QRScanTypeOther,
        str);
  }

  ///添加虚拟设备
  void _parseVirtualDev(String str, CompleteHandler complete) async {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseVirtualDev', 'str': str});
    str = str.replaceAll(new RegExp(r"\s+\b|\b\s"), "");
    Uri u = Uri.parse(str);
    Map urlParamer = u.queryParameters;
    String typeId = CommonUtil.convertType<String>(
        urlParamer[QRScanRequestString.QR_SCAN_REQUEST_TYPE_ID], '');
    String productCode = CommonUtil.convertType<String>(
        urlParamer[QRScanRequestString.QR_SCAN_REQUEST_PRODUCT_CODE], '');
    if (StringUtil.isBlankString(typeId) ||
        StringUtil.isBlankString(productCode)) {
      complete(QRScanErrorString.QR_NO_PARSE_PROMPT,
          QRScanType.QRScanTypeParseError, str);
      return;
    }

    bool connectionStatus = await InternetUtil.checkIsReachableNetwork();
    if (!connectionStatus) {
      complete(QRScanErrorString.QR_NO_NETWORK_PROMPT,
          QRScanType.QRScanTypeNoNetwork, str);
      return;
    }
    _traceEventWithVariable(QRString.QR_SCAN_VIRTUAL_DEV_MARK);
    var param = {
      "typeId": typeId,
      "productCode": productCode,
    };
    var response = await QRScanHttpService.post(
        ApiUrl.QRSCAN_VIRTUAL_DEVICE_LINK, QRScanSignType.sha256,
        param: param);
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseVirtualDev', 'response': response});
    if (response != null &&
        response is Map &&
        response[QRScanRequestString.QR_SCAN_REQUEST_RETCODE] ==
            QRScanRequestString.QR_SCAN_REQUEST_SUCCES) {
      final QrScanResponse virtualDevResponse =
          QrScanResponse.fromJson(response);
      final String deviceId =
          CommonUtil.convertType<String>(virtualDevResponse.data, '');
      if (deviceId != null &&
          deviceId is String &&
          !StringUtil.isBlankString(deviceId)) {
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: {'fn': '_parseVirtualDev', 'deviceId': deviceId});
        complete(deviceId, QRScanType.QRScanTypeVirtualDev, str);
      } else {
        complete(QRScanErrorString.QR_NO_PARSE_PROMPT,
            QRScanType.QRScanTypeNoNetwork, str);
      }
    } else {
      complete(QRScanErrorString.QR_VIRTUAL_DEVICE_CREATE_ERROR,
          QRScanType.QRScanTypeNoNetwork, str);
    }
  }

  ///授权登录
  Future<void> _parseAuthorizeLogine(
      String str, CompleteHandler complete) async {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseAuthorizeLogine', 'str': str});
    List<String> list = str.split(QRString.QR_SCAN_LOGIN_MARK);
    String uuid = '';
    if (list != null && list.length > 0) {
      uuid = list.last;
    }

    if (StringUtil.isBlankString(uuid)) {
      complete(QRScanErrorString.QR_NO_PARSE_PROMPT,
          QRScanType.QRScanTypeParseError, str);
    } else {
      _traceEventWithVariable(QRString.QR_SCAN_LOGIN_MARK);
      //授权登录绑定迁移-地址变为绑定页面
      String bindPage = '';
      final bool isH5BindingPage = await CommonUtil.getABTestDataForBind();
      if (!isH5BindingPage) {
        bindPage = QRString.QR_SCAN_BINDING_PAGE_URL;
      } else {
        bindPage = QRString.QR_SCAN_BINDING_PAGE_URL_FOR_H5;
        str = base64Url.encode(utf8.encode(str));
        bindPage = '$bindPage&codeType=QROauth&scanresult=$str';
      }

      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': '_parseAuthorizeLogine', 'bindPage': bindPage});
      complete(bindPage, QRScanType.QRScanTypeAuthorizeLogin, str);
    }
  }

  ///扫码加入家庭
  Future<void> _parseJoinFamily(
      String scanUrl, CompleteHandler complete) async {
    try {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: <String, String>{'fn': '_parseJoinFamily', 'scanUrl': scanUrl});
      if (JoinFamilyLoading.isShowing()) {
        return;
      }
      final bool connectionStatus =
          await InternetUtil.checkIsReachableNetwork();
      if (!connectionStatus) {
        complete(QRScanErrorString.QR_NO_NETWORK_PROMPT,
            QRScanType.QRScanTypeNoNetwork, scanUrl);
        return;
      }

      final UserInfo userInfo = await User.getUserInfo();
      String userFamilyName = userInfo.nickname;
      if (StringUtil.isBlankString(userFamilyName)) {
        userFamilyName = userInfo.mobile;
      }
      if (StringUtil.isBlankString(userFamilyName)) {
        complete(QRScanErrorString.QR_NO_PARSE_PROMPT,
            QRScanType.QRScanTypeParseError, scanUrl);
        return;
      }

      final String authCode = _fetchAuthCode(scanUrl);
      if (StringUtil.isBlankString(authCode)) {
        complete(QRScanErrorString.QR_NO_PARSE_PROMPT,
            QRScanType.QRScanTypeParseError, scanUrl);
        return;
      }
      final int? memberType = _fetchMemberType(scanUrl);
      final String? memberRole = _fetchMemberRole(scanUrl);
      JoinFamilyLoading.showLoading(QRScanCommonUtil().context);
      _traceEventWithVariable(QRString.QR_SCAN_ADD_FANILY_MARK);
      //请求参数
      final Map<String, dynamic> paramData = <String, dynamic>{
        'authCode': authCode,
        'userFamilyName': userFamilyName,
        'isAgree': 'true',
      };
      if (memberType != null && memberRole != null) {
        paramData['memberType'] = memberType;
        paramData['memberRole'] = Uri.decodeQueryComponent(memberRole);
      }
      LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, dynamic>{
        'fn': '_parseJoinFamily',
        'url': ApiUrl.QR_SCAN_INVITE_FAMILY,
        'paramData': paramData,
      });
      final Map<String, dynamic>? response = await QRScanHttpService.postNew(
          RequestUtil.getSerEnv(),
          ApiUrl.QR_SCAN_INVITE_FAMILY,
          QRScanSignType.sha256_for_zj,
          param: paramData);
      LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, dynamic>{
        'fn': '_parseJoinFamily',
        'url': ApiUrl.QR_SCAN_INVITE_FAMILY,
        'paramData': paramData,
        'response': response
      });
      if (response != null) {
        final QrScanResponse joinFamilyResponse =
            QrScanResponse.fromJson(response);
        if (joinFamilyResponse.retCode ==
            QRScanRequestString.QR_SCAN_REQUEST_SUCCES) {
          final dynamic map = joinFamilyResponse.data;
          if (map != null && map is Map<String, dynamic>) {
            final QrScanResponseData joinFamilyData =
                QrScanResponseData.fromJson(map);
            final String familyId = joinFamilyData.familyId ?? '';
            if (StringUtil.isBlankString(familyId)) {
              _showInvalidModalWidget(
                  complete,
                  QRScanErrorString.QR_ADD_FAMILY_TITLE_INVALID_CODE,
                  QRScanErrorString.QR_ADD_FAMILY_NEED_REFRESH);
            } else {
              LogHelper.debug(
                tag: QRScanLogString.tagQRScan,
                msg: <String, String>{
                  'fn': '_parseJoinFamily',
                  'familyId': familyId
                },
              );
              complete(familyId, QRScanType.QRScanTypeJoinFamily, scanUrl);
            }
          } else {
            _showInvalidModalWidget(
                complete,
                QRScanErrorString.QR_ADD_FAMILY_TITLE_INVALID_CODE,
                QRScanErrorString.QR_ADD_FAMILY_NEED_REFRESH);
          }
        } else {
          _dealAddFamilyError(response, complete, scanUrl);
        }
      } else {
        _showInvalidModalWidget(
            complete,
            QRScanErrorString.QR_ADD_FAMILY_TITLE_INVALID_CODE,
            QRScanErrorString.QR_ADD_FAMILY_NEED_REFRESH);
      }
    } catch (e) {
      LogHelper.error(
          tag: QRScanLogString.tagQRScan,
          msg: <String, String>{'fn': '_parseJoinFamily error $e'});
      _showInvalidModalWidget(
          complete,
          QRScanErrorString.QR_ADD_FAMILY_TITLE_INVALID_CODE,
          QRScanErrorString.QR_ADD_FAMILY_NEED_REFRESH);
    }
  }

  void _showInvalidModalWidget(
    CompleteHandler complete,
    String title,
    String desc,
  ) {
    if (QRScanCommonUtil().context == null) {
      LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, String>{
        'fn': '_showInvalidModalWidget context is null'
      });
      return;
    }
    JoinFamilyLoading.dismiss();
    if (Navigator.canPop(QRScanCommonUtil().context!)) {
      _needStartScan = false;
      Navigator.of(QRScanCommonUtil().context!).pop();
    }
    showModalBottomSheetWidget(
      QRScanCommonUtil().context!,
      title: title,
      inviteType: InviteType.invalid_code,
      child: _showInvalidModal(desc),
      onCancel: () {
        if (Navigator.canPop(QRScanCommonUtil().context!)) {
          Navigator.of(QRScanCommonUtil().context!).pop();
        }
      },
    ).whenComplete(() {
      // 开始扫码
      _needStartScan = true;
      rScanController?.startGetScanResult();
    });
  }

  void _parseWashCallLink(String scanUrl, CompleteHandler complete) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseWashCallLink', 'scanUrl': scanUrl});
    List<String> list = scanUrl.split('?');
    String devidStr = '';
    for (String let in list) {
      if (let.contains('devid=')) {
        devidStr = let;
        break;
      }
    }
    if (StringUtil.isBlankString(devidStr)) {
      complete(QRScanErrorString.QR_NO_PARSE_PROMPT,
          QRScanType.QRScanTypeParseError, scanUrl);
      return;
    } else {
      _traceEventWithVariable(QRString.QR_SCAN_WASHCALL_MARK);
      String washcallUrl = QRString.QR_SCAN_WASHCALL_URL + devidStr;
      complete(washcallUrl, QRScanType.QRScanTypeBuyJump, scanUrl);
    }
  }

  //help--
  //成功打点
  void _traceEventWithVariable(String str) {
    Trace.traceEventWithVariable(
        eventId: QRScanTraceCodeString.QR_SCAN_TRACE_CODE_SCAN_SUCESS,
        variable: {
          'time_length': _milliseconds.toString(),
          'content_type': _sourcetype == QRScanSourceType.QRScanSourceTypeCamera
              ? 1.toString()
              : _sourcetype == QRScanSourceType.QRScanSourceTypeAlbum
                  ? 2.toString()
                  : 0.toString(),
          'code': str,
          'value': _brightness == null ? '0' : _brightness.toString()
        });
  }

  bool _isLongLinkWithScanStr(String str) {
    if (str.contains(QRString.QR_SCAN_PRODUCTION_LONG_LINK_MARK) ||
        str.contains(QRString.QR_SCAN_ACCEPTANCE_LONG_LINK_MARK)) {
      return true;
    }
    return false;
  }

  bool _isVirtualDevStr(String str) {
    if (_isLongLinkWithScanStr(str)) {
      return (str.contains(QRString.QR_SCAN_VIRTUAL_DEV_MARK));
    }
    return false;
  }

  String _fetchShortLinkCode(String shortLink) {
    if (StringUtil.isBlankString(shortLink)) {
      return '';
    }
    final List<String> components = shortLink.split('?');
    if (components == null || components.length == 0) {
      return '';
    }
    String shortLinkPath = components.first;
    int index = shortLink.indexOf(QRString.QR_SCAN_SHORT_LINK_MARK);
    if (index == -1) {
      return '';
    }
    String result = shortLinkPath
        .substring(index + QRString.QR_SCAN_SHORT_LINK_MARK.length);
    return result;
  }

  String _componentsUrlStr(String str, Map param) {
    //没有参数，不需要拼接
    if (param == null || param.length == 0) {
      return str;
    }
    //如果没有?号添加?号
    int index = str.indexOf('?');
    if (index < 0) {
      str += '?';
    }

    Map paramMap = Map();
    //参数字典不为空的时候需要重拍数组
    if (index < str.length - 1) {
      //此做法默认链接中只有一个问号
      final List<String> list = str.split('?');
      String paramStr;
      //原来带了参数
      if (list.length > 1) {
        str = list[0] + '?';
        paramStr = list[1];
        List paramList = paramStr.split('&');
        paramList.forEach((element) {
          String e = element as String;
          List eList = e.split('=');
          if (eList.length > 1) {
            paramMap[eList[0]] = eList[1];
          }
        });
      }
    }
    //将原来的数据放在参数的最前面
    if (paramMap.length > 0) {
      paramMap.addAll(param);
      param = paramMap;
    }
    //读取参数并组成带参数的url
    if (param != null) {
      param.forEach((key, value) {
        str += (CommonUtil.convertType<String>(key, '') +
            '=' +
            value.toString() +
            '&');
      });
      //此处多了一个&，去掉它
      str = str.substring(0, str.length - 1);
    }
    return str;
  }

  String _fetchAuthCode(String code) {
    int index = code.indexOf(QRString.QR_SCAN_ADD_FANILY_MARK);
    if (index < 0) {
      return '';
    }
    return code.substring(index + QRString.QR_SCAN_ADD_FANILY_MARK.length);
  }

  int? _fetchMemberType(String code) {
    const String str = 'memberType=';
    final int index = code.indexOf(str);
    if (index < 0) {
      return null;
    }
    final int endIndex = code.indexOf('&', index);
    if (endIndex < 0) {
      return null;
    }
    return int.tryParse(code.substring(index + str.length, endIndex));
  }

  String? _fetchMemberRole(String code) {
    const String str = 'memberRole=';
    final int index = code.indexOf(str);
    if (index < 0) {
      return null;
    }
    final int endIndex = code.indexOf('&', index);
    if (endIndex < 0) {
      return null;
    }
    return code.substring(index + str.length, endIndex);
  }

  void _dealAddFamilyError(Map<String, dynamic> response,
      CompleteHandler complete, String originalStr) {
    if (response[QRScanRequestString.QR_SCAN_REQUEST_RETCODE] ==
        QRScanRequestErrorCode.QR_SCAN_ADD_FAMILY_ERROR_E31108) {
      _showInvalidModalWidget(
          complete,
          QRScanErrorString.QR_ADD_FAMILY_TITLE_FAILED,
          QRScanErrorString.QR_ADD_FAMILY_DISSOLUTION);
    } else if (response[QRScanRequestString.QR_SCAN_REQUEST_RETCODE] ==
        QRScanRequestErrorCode.QR_SCAN_ADD_FAMILY_ERROR_E31405) {
      _showInvalidModalWidget(
          complete,
          QRScanErrorString.QR_ADD_FAMILY_TITLE_FAILED,
          QRScanErrorString.QR_ADD_FAMILY_MEMBER_OVERFLOW_ERROR);
    } else if (response[QRScanRequestString.QR_SCAN_REQUEST_RETCODE] ==
        QRScanRequestErrorCode.QR_SCAN_ADD_FAMILY_ERROR_E31406) {
      _showInvalidModalWidget(
          complete,
          QRScanErrorString.QR_ADD_FAMILY_TITLE_FAILED,
          QRScanErrorString.QR_ADD_FAMILY_OVERFLOW_ERROR);
    } else if (response[QRScanRequestString.QR_SCAN_REQUEST_RETCODE] ==
        QRScanRequestErrorCode.QR_SCAN_ADD_FAMILY_ERROR_E31105) {
      _showInvalidModalWidget(
          complete,
          QRScanErrorString.QR_ADD_FAMILY_TITLE_FAILED,
          QRScanErrorString.QR_ADD_FAMILY_ALREADY_INVITED);
    } else {
      _showInvalidModalWidget(
          complete,
          QRScanErrorString.QR_ADD_FAMILY_TITLE_INVALID_CODE,
          QRScanErrorString.QR_ADD_FAMILY_NEED_REFRESH);
    }
  }

  Future<void> _dealScanToJoinFamilyLogic(
      String str, CompleteHandler handler) async {
    // 暂停扫码
    rScanController?.stopGetScanResult();
    //未登录提示错误，不处理
    final LoginStatus status = await User.getLoginStatus();
    if (!status.isLogin) {
      handler(QRScanErrorString.QR_NOT_LOGIN_PROMPT,
          QRScanType.QRScanTypeParseError, str);
      rScanController?.startGetScanResult();
      return;
    }

    //无网络提示错误，不处理
    final bool connectionStatus = await InternetUtil.checkIsReachableNetwork();
    if (!connectionStatus) {
      handler(QRScanErrorString.QR_NO_NETWORK_PROMPT,
          QRScanType.QRScanTypeNoNetwork, str);
      rScanController?.startGetScanResult();
      return;
    }

    _parseQRCodeInfo(str, handler);
  }

  // 获取二维码信息
  Future<void> _parseQRCodeInfo(String qrCode, CompleteHandler complete) async {
    try {
      final String code = _fetchAuthCode(qrCode);
      final Map<String, dynamic>? res = await QRScanHttpService.postNew(
        RequestUtil.getSerEnv(),
        ApiUrl.QR_SCAN_GET_QRCODE_INFO,
        QRScanSignType.sha256_for_zj,
        param: <String, dynamic>{
          'code': code,
        },
      );
      LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: <String, dynamic>{
          'fn': '_parseQRCodeInfo',
          'result': res,
        },
      );
      if (QRScanCommonUtil().context == null) {
        LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: <String, String>{
            'fn': '_parseQRCodeInfo',
            'info': 'QRScanCommonUtil().context == null'
          },
        );
        rScanController?.startGetScanResult();
        return;
      }
      if (res != null && res['data'] is Map<String, dynamic>) {
        final QRCodeInfoModel model =
            QRCodeInfoModel.fromJson(res['data'] as Map<String, dynamic>);
        showModalBottomSheetWidget(
          QRScanCommonUtil().context!,
          title: '家庭邀请',
          inviteType: InviteType.invite_family,
          child: _showInviteFamilyModal(
            inviteUserName: model.inviteUserName,
            inviteFamilyName: model.familyName,
            inviteAvatarUrl: model.inviteAvatarUrl,
          ),
          onCancel: () {
            if (Navigator.canPop(QRScanCommonUtil().context!)) {
              Navigator.of(QRScanCommonUtil().context!).pop();
            }
          },
          onConfirm: () {
            _parseJoinFamily(qrCode, complete);
          },
        ).whenComplete(() {
          // 开始扫码
          if (_needStartScan) {
            rScanController?.startGetScanResult();
          }
        });
      } else {
        _showInvalidModalWidget(
            complete,
            QRScanErrorString.QR_ADD_FAMILY_TITLE_INVALID_CODE,
            QRScanErrorString.QR_ADD_FAMILY_NEED_REFRESH);
      }
    } catch (e) {
      rScanController?.startGetScanResult();
      LogHelper.error(
        tag: QRScanLogString.tagQRScan,
        msg: <String, Object>{
          'fn': '_parseQRCodeInfo',
          'err': e,
        },
      );
    }
  }

  Widget _showInvalidModal(String desc) {
    return Container(
      width: 326,
      margin: const EdgeInsets.only(bottom: 16),
      child: Text(
        desc,
        textAlign: TextAlign.center,
        style: TextStyle(
          color: const Color(0xFF666666),
          fontSize: 14,
          fontFamilyFallback: Platform.isIOS ? <String>['PingFang SC'] : null,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget _showInviteFamilyModal({
    required String inviteUserName,
    required String inviteFamilyName,
    required String inviteAvatarUrl,
  }) {
    return Container(
      width: 326,
      margin: const EdgeInsets.only(top: 40, bottom: 40),
      child: Column(
        children: <Widget>[
          Container(
            width: 88,
            height: 88,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(44),
            ),
            child: ClipOval(
              child: Image.network(
                inviteAvatarUrl,
                errorBuilder: (BuildContext context, Object error,
                    StackTrace? stackTrace) {
                  return Image.asset(
                    'images/avatar.png',
                    package: 'scan',
                    width: 88,
                    height: 88,
                  );
                },
              ),
            ),
          ),
          const SizedBox(
            height: 12,
          ),
          Text(
            inviteUserName,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: const Color(0xFF111111),
              fontSize: 17,
              fontFamilyFallback:
                  Platform.isIOS ? <String>['PingFang SC'] : null,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(
            height: 8,
          ),
          SizedBox(
            width: 334,
            child: Text(
              '邀请您加入“$inviteFamilyName”，共同控制家电',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: const Color(0xFF666666),
                fontSize: 14,
                fontFamilyFallback:
                    Platform.isIOS ? <String>['PingFang SC'] : null,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _parseSecurity(String str, CompleteHandler complete) async {
    bool connectionStatus = await InternetUtil.checkIsReachableNetwork();
    if (!connectionStatus) {
      complete(QRScanErrorString.QR_NO_NETWORK_ERROR,
          QRScanType.QRScanTypeNoNetwork, str);
      return;
    }
    LoginStatus status = await User.getLoginStatus();
    if (status.isLogin != true) {
      complete(QRScanErrorString.QR_NOT_LOGIN_PROMPT,
          QRScanType.QRScanTypeParseError, str);
      return;
    }
    //解析扫码结果获取任务ID
    if (str.isEmpty) {
      complete(QRScanErrorString.QR_NO_PARSE_PROMPT,
          QRScanType.QRSecurityInfoError, str);
      return;
    }
    String firstSubString =
        str.substring(QRString.QR_SCAN_SECURITY_CODE_MARK.length);
    if (firstSubString.indexOf("\$") == -1) {
      complete(QRScanErrorString.QR_NO_PARSE_PROMPT,
          QRScanType.QRSecurityInfoError, str);
      return;
    }
    String taskId = firstSubString.substring(0, firstSubString.indexOf("\$"));
    //请求参数
    Map<String, dynamic> paramMap = Map<String, dynamic>();
    paramMap['taskId'] = taskId;
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseSecurity', 'taskId': taskId});

    final _timestamp = HTTP.getTimestamp();
    AppInfoModel appInfoModel = await AppInfoPlugin.getAppInfo();

    String sign = HTTP.sign(METHOD.POST, ApiUrl.QR_SCAN_SECURITY_URL, paramMap,
        appInfoModel.appId, appInfoModel.appKey, _timestamp);

    Map<String, dynamic> _headersInfo =
        await HTTP.headers({"sign": sign, 'timestamp': _timestamp});

    var response = await httpManager.postData(
      RequestUtil.getSerEnv() + '${ApiUrl.QR_SCAN_SECURITY_URL}',
      params: paramMap,
      header: _headersInfo,
    );
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_parseSecurity', 'result': response});
    if (response != null && response is Map<String, dynamic>) {
      try {
        final QrScanResponse securityResponse =
            QrScanResponse.fromJson(response);
        String? retCode = securityResponse.retCode;
        dynamic data = securityResponse.data;
        if (retCode == null) {
          complete(QRScanErrorString.QR_TASK_INFO_ERROR,
              QRScanType.QRSecurityInfoError, str);
          return;
        }
        if (retCode == QRScanRequestString.QR_SCAN_REQUEST_SUCCES) {
          if (data == null || data is! Map<String, dynamic>) {
            complete(QRScanErrorString.QR_TASK_INFO_ERROR,
                QRScanType.QRSecurityInfoError, str);
            return;
          }
          final QrScanResponseData securityResponseData =
              QrScanResponseData.fromJson(data);
          int? state = securityResponseData.taskState;
          if (state != null) {
            if (state == SecurityTaskState.SecurityNormal ||
                state == SecurityTaskState.SecurityPause) {
              data['taskId'] = taskId;
              complete(ApiUrl.QR_SCAN_SECURITY_URL, QRScanType.QRSecurity,
                  jsonEncode(data));
            } else {
              complete(QRScanErrorString.QR_TASK_INFO_ERROR,
                  QRScanType.QRSecurityInfoError, str);
            }
          } else {
            complete(QRScanErrorString.QR_TASK_INFO_ERROR,
                QRScanType.QRSecurityInfoError, str);
          }
        } else if (retCode == QRScanRequestString.QR_SCAN_TASK_NO_EXIST) {
          complete(QRScanErrorString.QR_TASK_INFO_ERROR,
              QRScanType.QRSecurityInfoError, str);
        } else if (retCode == QRScanRequestString.QR_SCAN_OVER_MAX) {
          complete(QRScanErrorString.QR_TASK_OVER_ERROR,
              QRScanType.QRSecurityInfoError, str);
        } else {
          complete(QRScanErrorString.QR_TASK_INFO_ERROR,
              QRScanType.QRSecurityInfoError, str);
        }
      } catch (e) {
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: {'fn': '_parseSecurity', 'catch': e});
      }
    } else {
      complete(QRScanErrorString.QR_TASK_INFO_ERROR,
          QRScanType.QRScanTypeNoNetwork, ApiUrl.QR_SCAN_SECURITY_URL);
    }
  }
}
