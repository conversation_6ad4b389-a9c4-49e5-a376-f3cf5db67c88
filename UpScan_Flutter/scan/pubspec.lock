# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  Appinfos:
    dependency: "direct main"
    description:
      name: Appinfos
      sha256: "6ac2368d971dff4edcf3d78d40f3c50a67d93903a1e814acbed4f8224a64c18f"
      url: "http://**************:8083"
    source: hosted
    version: "1.13.0+2025041701"
  abtest:
    dependency: "direct main"
    description:
      name: abtest
      sha256: dbd50c33fdeefc48a7dbf61cb392d73dde09e5dff92c7d76bc61cc06d2578b53
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025033101"
  annotation:
    dependency: transitive
    description:
      name: annotation
      sha256: e7aa3a0746fcca2d30d8f2eb834e6d7a32371658ca41736ba44b243f1bdb5026
      url: "http://**************:8083"
    source: hosted
    version: "0.0.11"
  async:
    dependency: transitive
    description:
      name: async
      sha256: d2872f9c19731c2e5f10444b14686eb7cc85c76274bd6c16e1816bff9a3bab63
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.12.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "8aab1771e1243a5063b8b0ff68042d67334e3feab9e95b9490f9a6ebf73b42ea"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.2"
  camera:
    dependency: transitive
    description:
      name: camera
      sha256: f980fa9d884ed2d5fff02b8025b369a6f70f1dec3941e7d0b19ee07a82f2eebc
      url: "http://**************:8083"
    source: hosted
    version: "1.0.1"
  camera_camera:
    dependency: "direct main"
    description:
      name: camera_camera
      sha256: "89d9f8755855c11904ea07402e8efad997cbfe722f492c4727b54fc210e1b31e"
      url: "http://**************:8083"
    source: hosted
    version: "8.2.0+2023111401"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.4.0"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.2"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.19.1"
  convert:
    dependency: "direct main"
    description:
      name: convert
      sha256: b30acd5944035672bc15c6b7a8b47d773e41e2f17de064350988c5d02adb1c68
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.6"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "253a18bbd4851fecba42f7343a1df3a9a4c1d31a2c1b37e221086b4fa8c8dbc9"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.8.0+1"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "7586e476d70caecaf1686d21eee7247ea43ef5c345eab9e0cc3583ff13378d78"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  event_bus:
    dependency: transitive
    description:
      name: event_bus
      sha256: "44baa799834f4c803921873e7446a2add0f3efa45e101a054b1f0ab9b95f8edc"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.0"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "6a95e56b2449df2273fd8c45a662d6947ce1ebb7aafe80e550a3f68297f3cacc"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.2"
  family:
    dependency: "direct main"
    description:
      name: family
      sha256: "04e9355ad2b481883b94cb8397ba54fd157bdc82b1996ada5a645ccb9b3b8492"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025041801"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "289279317b4b16eb2bb7e271abccd4bf84ec9bdcbe999e278a94b804f5630418"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.4"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_native_image:
    dependency: transitive
    description:
      name: flutter_native_image
      sha256: "2a0d53e50bdd6ddc681cb2697554971d6b4bd986fdc0f66fb75fd73da7877be4"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.0.6"
  flutter_redux:
    dependency: "direct main"
    description:
      name: flutter_redux
      sha256: "8985fd9a4f4016be6acc058818b7bc4cd43f65d134e6507a6e017ac5b499cd82"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.8.2"
  flutter_screenutil:
    dependency: "direct main"
    description:
      name: flutter_screenutil
      sha256: "03714dd992a90453f2eb48c08d1caa0769d366ee5dc647cf9d23c5b320dab2cb"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.0.0+2"
  flutter_spinkit:
    dependency: "direct main"
    description:
      name: flutter_spinkit
      sha256: d2696eed13732831414595b98863260e33e8882fc069ee80ec35d4ac9ddb0472
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.2.1"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      sha256: "25e51620424d92d3db3832464774a6143b5053f15e382d8ffbfd40b6e795dcf1"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "8.2.12"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.13.6"
  http_client_helper:
    dependency: transitive
    description:
      name: http_client_helper
      sha256: "14c6e756644339f561321dab021215475ba4779aa962466f59ccb3ecf66b36c3"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.4"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "178d74305e7866013777bab2c3d8726205dc5a4dd935297175b19a23a2e66571"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.1.2"
  image_crop:
    dependency: transitive
    description:
      name: image_crop
      sha256: "9a299c15dedcef2637469ff7d6321751fa21404f95cdbb67a80c75db335fc31c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.0"
  image_editor:
    dependency: transitive
    description:
      name: image_editor
      sha256: "46a7db6b244554c80153b54c2ab0a63e17a6c36f146b7041e321c346ede312a5"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.0"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: c35baad643ba394b40aac41080300150a4f08fd0fd6a10378f8f7c6bc161acec
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "10.0.8"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: f8b613e7e6a13ec79cfdc0e97638fddb3ab848452eff057653abd3edba760573
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.9"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.1"
  log:
    dependency: "direct main"
    description:
      name: log
      sha256: a38d0b2df4d7feec05c3e7ecc2d1eb021999d9bf79649705a8efe0eb0d41b6ad
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024102901"
  main_business:
    dependency: transitive
    description:
      name: main_business
      sha256: d9f301aba65ec6902f2725a2bd0fc271e0745b7752b0242f0831773cd9d17247
      url: "http://**************:8083"
    source: hosted
    version: "2.1.0+1b2022042801"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.11.1"
  message:
    dependency: "direct main"
    description:
      name: message
      sha256: "66c0b214d1a3409f90ff0db72f74dce9a8b2a9582979897af87d28db92194089"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025040101"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.16.0"
  multiengines:
    dependency: transitive
    description:
      name: multiengines
      sha256: "8935ac69163e28d22d23aac5246e60f7fee0466d32378de88ae0b43cfb4595e6"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025042702"
  native_device_orientation:
    dependency: transitive
    description:
      name: native_device_orientation
      sha256: ea0b70eb34a2484e51888e64381d9aaefc10fce45f1b029d737a873f9dbf164c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.0"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.9.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: cfdc261c62a7273be7e051b19d27e503927a40919932f790681042a038f3605d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.2"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.1"
  path_provider_macos:
    dependency: transitive
    description:
      name: path_provider_macos
      sha256: cd57cb98a30ce9d12fdd1896d9d3b0517ce689f942de6ccd2708cd39b3d18a7c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.7"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.0"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      sha256: "67fc27ed9639506c856c840ccce7594d0bdcd91bc8d53d6e52359449a1d50602"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.11.1"
  photo:
    dependency: "direct main"
    description:
      name: photo
      sha256: ca84b0931c00fc5cb63a1fec7c29d0bd5e9743ef365a0577e244b5afb716839c
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+**********"
  photo_manager:
    dependency: transitive
    description:
      name: photo_manager
      sha256: "383f5ab7bd77e3b31cdb4a7e8c5cbce59412497fdcf5efa461e6fde0d728d41a"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+**********"
  photo_view:
    dependency: transitive
    description:
      name: photo_view
      sha256: "871dbaa38dac9b5e7e1e1ff59027459f82a67fcbc507fef2f6146b7d6f422af6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.11.1"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.6"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.8"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: ea0b925899e64ecdfbf9c7becb60d5b50e706ade44a85b2363be2a22d88117d2
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.2.2"
  r_scan:
    dependency: "direct main"
    description:
      name: r_scan
      sha256: "1e23d78603a187b22cb61448c0f1a7dd958f70cfbef30c0b360a0cce6b2100dd"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025021301"
  redux:
    dependency: "direct main"
    description:
      name: redux
      sha256: "1e86ed5b1a9a717922d0a0ca41f9bf49c1a587d50050e9426fc65b14e85ec4d7"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.0.0"
  resource:
    dependency: "direct main"
    description:
      name: resource
      sha256: ad9e564c343ec8285a9db1874d4821ff771fd9c7783c4813a810bb4ce634b8f5
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025011401"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: e59cd2626a88e4543d5940fa3e128219fb747bf8cac1793cd0c4ed8243e853d9
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.27.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "254ee5351d6cb365c859e20ee823c3bb479bf4a293c22d17a9f1bf144ce86f7c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.10.1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.12.1"
  storage:
    dependency: "direct main"
    description:
      name: storage
      sha256: "72f30da3d1ecce24c32ac3eb073a39223ad149c56171cd32795e340a98aa43fa"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024061701"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.4"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "921cd31725b72fe181906c6a94d987c78e3b98c2e205b397ea399d4054872b43"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.4.1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: "7f554798625ea768a7518313e58f83891c7f5024f88e46e7182a4558850a4b8e"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.2"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: fb31f383e2ee25fbbfe06b40fe21e1e458d14080e3c67e7ba0acfde4df4e0bbd
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.7.4"
  trace:
    dependency: "direct main"
    description:
      name: trace
      sha256: "6cbd27024a7b93826d8c06bbee3888551983ff86bb054c990ff8fc6a962e9ef3"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025011401"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: f9049c039ebfeb4cf7a7104a675823cd72dba8297f264b6637062516699fa006
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.4.0"
  uimessage:
    dependency: transitive
    description:
      name: uimessage
      sha256: "534c0a2ac4350544641bb64f612378aa677d53743d3899a1359ed148a4c8ac6d"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025032601"
  uplustrace:
    dependency: "direct main"
    description:
      name: uplustrace
      sha256: "66feb5f0fb2e891a0877f5ba629c02914ba77000e6afb88c565397c245a7abad"
      url: "http://**************:8083"
    source: hosted
    version: "1.2.0+2024012601"
  uppermission:
    dependency: "direct main"
    description:
      name: uppermission
      sha256: "2abdc0f3987c37e0298444642c74fff0674b71f6168526ace9fd77d60fba993a"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024060301"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: "9d06212b1362abc2f0f0d78e6f09f726608c74e3b9462e8368bb03314aa8d603"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.3.1"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "8582d7f6fe14d2652b4c45c9b6c14c0b678c2af2d083a11b604caeba51930d79"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.3.16"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "7f2022359d4c099eea7df3fdf739f7d3d3b9faf3166fb1dd390775176e0b76cb"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.3.3"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "4e9ba368772369e3e08f231d2301b4ef72b9ff87c31192ef471b380ef29a4935"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.2.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "17ba2000b847f334f16626a574c702b196723af2a289e7a93ffcb79acff855c2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.2.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "4bd2b7b4dc4d4d0b94e5babfffbca8eac1a126c7f3d6ecbc1a11013faa3abba2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.1"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "3284b6d2ac454cf34f114e1d3319866fdd1e19cdc329999057e44ffe936cfa77"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.4"
  user:
    dependency: "direct main"
    description:
      name: user
      sha256: af9ad76ed1f9d8864ed6a2a359661695c86c6b3091bf816d02bcfdb54e4909e4
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025041601"
  vdn:
    dependency: "direct main"
    description:
      name: vdn
      sha256: beb1d1db62af4fdf05f383c4ab18ff08765943d78bf83341ae19935f5eb742f0
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025012301"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.4"
  video_player:
    dependency: transitive
    description:
      name: video_player
      sha256: "21dac219ecef5588a58c27d493d4969f911d8fdc0e3d2247165d7cafa1dbd3e3"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025010201"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: "33107b16b90d8a7fafd00e17983090ece6daa3e49f5b6dba63fa7380ac2bfae5"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025010202"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: "0968250880a6c5fe7edc067ed0a13d4bae1577fe2771dcf3010d52c4a9d3ca14"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "14.3.1"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "868d88a33d8a87b18ffc05f9f030ba328ffefba92d6c127917a2ba740f9cfe4a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.1"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
sdks:
  dart: ">=3.7.0 <4.0.0"
  flutter: ">=3.27.0"
