package com.uplus.upscan.core.handlers;

/**
 * 扫码结果来源枚举
 * 定义扫码结果的来源类型，对应Flutter中的QRScanSourceType
 */
public enum UpScanResultSource {
    
    /**
     * 相机扫码
     * 对应Flutter中的QRScanSourceTypeCamera
     */
    CAMERA("camera", "相机"),
    
    /**
     * 相册扫码
     * 对应Flutter中的QRScanSourceTypeAlbum
     */
    ALBUM("album", "相册");
    
    private final String type;
    private final String description;
    
    UpScanResultSource(String type, String description) {
        this.type = type;
        this.description = description;
    }
    
    /**
     * 获取类型标识
     * @return 类型标识
     */
    public String getType() {
        return type;
    }
    
    /**
     * 获取类型描述
     * @return 类型描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据类型标识获取枚举
     * @param type 类型标识
     * @return 对应的枚举，如果不存在则返回null
     */
    public static UpScanResultSource fromType(String type) {
        if (type == null) {
            return null;
        }
        
        for (UpScanResultSource source : values()) {
            if (source.type.equals(type)) {
                return source;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为相机扫码
     * @return true表示相机扫码，false表示其他来源
     */
    public boolean isCamera() {
        return this == CAMERA;
    }
    
    /**
     * 判断是否为相册扫码
     * @return true表示相册扫码，false表示其他来源
     */
    public boolean isAlbum() {
        return this == ALBUM;
    }
    
    @Override
    public String toString() {
        return "UpScanResultSource{" +
                "type='" + type + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
