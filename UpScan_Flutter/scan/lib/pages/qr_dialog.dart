import 'dart:ui';


import 'package:Appinfos/Appinfos.dart';
import 'package:Appinfos/AppinfosModel.dart' as AppModel;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:log/log.dart';
import 'package:log/log_modle.dart';
import 'package:r_scan/r_scan.dart';
import 'package:scan/config/qr_sting.dart';
import 'package:uplustrace/uplustrace.dart';
import 'package:uppermission/uppermission.dart';

import '../config/constant.dart';

class RSNcanDialog extends StatefulWidget {
  @override
  _RSNcanDialogState createState() => _RSNcanDialogState();
}

class _RSNcanDialogState extends State<RSNcanDialog> {
  RScanController? _controller;

  @override
  void initState() {
    super.initState();
    initController();
  }

  bool isFirst = true;

  Future<void> initController() async {
    _controller = RScanController();
    _controller?.addListener(() {
      final result = _controller?.result;
      if (result != null) {
        if (isFirst) {
          Navigator.of(context).pop(result);
          isFirst = false;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    double boxWidth = ScreenUtil().screenWidth * 2 / 3;
    return Scaffold(
      backgroundColor: Colors.black,
      body: FutureBuilder<bool>(
        future: canOpenCameraView(),
        builder: (BuildContext context, AsyncSnapshot<bool> snapshot) {
          if (snapshot.hasData && snapshot.data == true) {
            return Stack(
              children: <Widget>[
                ScanImageView(
                  boxWidth: boxWidth,
                  child: RScanView(
                    controller: _controller!,
                  ),
                ),
                Align(
                    alignment: Alignment.bottomCenter,
                    child: FutureBuilder(
                      future: getFlashMode(),
                      builder: _buildFlashBtn,
                    ))
              ],
            );
          } else {
            return Container();
          }
        },
      ),
    );
  }

  Future<bool> getFlashMode() async {
    bool isOpen = false;
    try {
      isOpen = (await _controller?.getFlashMode())!;
    } catch (_) {}
    return isOpen;
  }

  Future<bool> canOpenCameraView() async {
    final result = await Uppermission.requestPermission(['camera']);
    if (result.isAllowed == false) {
      return false;
    }
    return true;
  }

  Widget _buildFlashBtn(BuildContext context, AsyncSnapshot<bool> snapshot) {
    return snapshot.hasData
        ? Padding(
            padding: EdgeInsets.only(
                bottom: 24 + MediaQuery.of(context).padding.bottom),
            child: IconButton(
                icon: Icon(snapshot.data! ? Icons.flash_on : Icons.flash_off),
                color: Colors.white,
                iconSize: 46,
                onPressed: () {
                  if (snapshot.data!) {
                    _controller?.setFlashMode(false);
                  } else {
                    _controller?.setFlashMode(true);
                  }
                  setState(() {});
                }),
          )
        : Container();
  }
}

class ScanImageView extends StatefulWidget {
  final Widget? child;
  final bool? needAnimate;
  @required
  final double boxWidth;

  const ScanImageView(
      {Key? key, required this.boxWidth, this.child, this.needAnimate})
      : super(key: key);
  @override
  StatefulElement createElement() {
    WidgetsBinding.instance!.addPostFrameCallback((timeStamp) {
      UplusTrace.finishTrack(QRScanTraceCodeString.QR_SCAN_TRACE_CODE_PAGE_LOAD_TIME);
    });
    return super.createElement();
  }

  @override
  _ScanImageViewState createState() => _ScanImageViewState();
}

class _ScanImageViewState extends State<ScanImageView>
    with TickerProviderStateMixin {
  AnimationController? controller;
  AppType appType = AppType.zhijia;
  Color borderColor = Colors.white;
  Color cornerColor = Colors.blue;
  Color scanColor = Colors.green;

  @override
  void initState() {
    super.initState();
    getAppType().then((value) {
      setState(() {
        appType = value;
        cornerColor =
            value == AppType.sanyiniao ? Color(0xFFBE965A) : Colors.blue;
        scanColor =
            value == AppType.sanyiniao ? Color(0xFFFF8900) : Colors.green;
      });
    }).catchError((e) {
      Log.printLog(LogModle('debug', '[photo>appType]', 'value :$e'));
    });
    controller = AnimationController(
        vsync: this, duration: Duration(milliseconds: 1000));
    controller?.repeat(reverse: true);
  }

  Future<AppType> getAppType() async {
    AppModel.AppInfoModel infoModel = await AppInfoPlugin.getAppInfo();
    if (infoModel.appId == CONSTANT.synAppIdAndroid ||
        infoModel.appId == CONSTANT.synAppIdIos) {
      return AppType.sanyiniao;
    } else {
      return AppType.zhijia;
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
        animation: controller!,
        builder: (BuildContext context, Widget? child) => CustomPaint(
              foregroundPainter: _ScanPainter(
                  controller!.value,
                  widget.boxWidth,
                  borderColor,
                  cornerColor,
                  scanColor,
                  widget.needAnimate!,
                  appType),
              child: widget.child,
              willChange: true,
            ));
  }
}

class _ScanPainter extends CustomPainter {
  final double value;
  final Color borderColor;
  final Color cornerColor;
  final Color scanColor;
  final bool needAnimate;
  final AppType appType;
  @required
  final double boxWidth;

  _ScanPainter(this.value, this.boxWidth, this.borderColor, this.cornerColor,
      this.scanColor, this.needAnimate, this.appType);

  Paint? _paint;

  @override
  void paint(Canvas canvas, Size size) {
    if (_paint == null) {
      initPaint();
    }
    double width = size.width;
    //double height = size.height;

    double boxWidth = this.boxWidth;
    // size.width * 2 / 3;

    double boxHeight = boxWidth;

    double left = (width - boxWidth) / 2;
    double top = 90 + MediaQueryData.fromWindow(window).padding.top + 40;
    // height / 4 - 35 + MediaQueryData.fromWindow(window).padding.top;
    double bottom = top + boxHeight;
    double right = left + boxWidth;
    _paint!.color = borderColor;
    final rect = Rect.fromLTWH(left, top, boxWidth, boxHeight);
    canvas.drawRect(rect, _paint!);

    _paint!.strokeWidth = 3;
    _paint!.color = cornerColor;
    Path path1 = Path()
      ..moveTo(left, top + 10)
      ..lineTo(left, top)
      ..lineTo(left + 10, top);
    canvas.drawPath(path1, _paint!);
    Path path2 = Path()
      ..moveTo(left, bottom - 10)
      ..lineTo(left, bottom)
      ..lineTo(left + 10, bottom);
    canvas.drawPath(path2, _paint!);
    Path path3 = Path()
      ..moveTo(right, bottom - 10)
      ..lineTo(right, bottom)
      ..lineTo(right - 10, bottom);
    canvas.drawPath(path3, _paint!);
    Path path4 = Path()
      ..moveTo(right, top + 10)
      ..lineTo(right, top)
      ..lineTo(right - 10, top);
    canvas.drawPath(path4, _paint!);

    _paint!.color = scanColor;

    if (needAnimate) {
      final scanRect = Rect.fromLTWH(
          left + 10, top + 10 + (value * (boxHeight - 20)), boxWidth - 20, 1);
      if (appType == AppType.sanyiniao) {
        _paint!.shader = LinearGradient(colors: <Color>[
          Color(0xFFBE965A),
          Color(0xFFFF8900),
          Color(0xFFBE965A),
        ], stops: [
          0.0,
          0.5,
          1,
        ]).createShader(scanRect);
      } else {
        _paint!.shader = LinearGradient(colors: <Color>[
          Colors.blue,
          Colors.blueAccent,
          Colors.blue,
        ], stops: [
          0.0,
          0.5,
          1,
        ]).createShader(scanRect);
      }

      canvas.drawRect(scanRect, _paint!);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }

  void initPaint() {
    _paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1
      ..isAntiAlias = true
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;
  }
}
