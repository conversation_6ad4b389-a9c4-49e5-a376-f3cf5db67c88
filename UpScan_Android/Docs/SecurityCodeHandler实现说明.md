# SecurityCodeHandler 实现说明

## 概述

SecurityCodeHandler 是处理以"MIGRATE_QR$"开头的安全迁移码的责任链Handler，严格按照Flutter中`_parseSecurity`方法的逻辑实现。

## 功能对应关系

### Flutter vs Android 实现对比

| Flutter实现 | Android实现 | 说明 |
|------------|-------------|------|
| `_parseSecurity` | `doHandle` | 主要处理逻辑 |
| `InternetUtil.checkIsReachableNetwork()` | `checkNetworkStatus()` | 网络状态检查 |
| `User.getLoginStatus()` | `checkLoginStatus()` | 登录状态检查 |
| 任务ID解析逻辑 | `parseTaskId()` | 从扫码结果中提取任务ID |
| HTTP网络请求 | `requestSecurityTaskInfo()` | 查询安全任务信息 |
| 响应处理逻辑 | `handleSecurityResponse()` | 处理服务器响应 |
| 页面跳转 | `navigateToSecurityPage()` | 跳转到安全迁移页面 |

## 处理流程

### 1. 扫码识别
```java
@Override
public boolean canHandle(String code) {
    return !TextUtils.isEmpty(code) && code.startsWith(SECURITY_CODE_MARK);
}
```
- 判断扫码结果是否以"MIGRATE_QR$"开头
- 对应Flutter中的条件：`str.startsWith(QRString.QR_SCAN_SECURITY_CODE_MARK)`

### 2. 前置检查
```java
// 暂停扫码
pauseScan();

// 检查网络状态
boolean hasNetwork = checkNetworkStatus();
if (!hasNetwork) {
    showErrorToast(ERROR_NO_NETWORK);
    resumeScan();
    return;
}

// 检查登录状态
boolean isLoggedIn = checkLoginStatus();
if (!isLoggedIn) {
    showErrorToast(ERROR_NOT_LOGIN);
    resumeScan();
    return;
}
```

### 3. 任务ID解析
```java
private String parseTaskId(String code) {
    String firstSubString = code.substring(SECURITY_CODE_MARK.length());
    int dollarIndex = firstSubString.indexOf("$");
    if (dollarIndex == -1) {
        return null;
    }
    return firstSubString.substring(0, dollarIndex);
}
```
- 去掉前缀"MIGRATE_QR$"
- 查找下一个"$"符号
- 提取中间的任务ID

### 4. 网络请求
```java
private void requestSecurityTaskInfo(String taskId, String originalCode) {
    JSONObject params = new JSONObject();
    params.put("taskId", taskId);
    // 发起网络请求
}
```
- 构建请求参数
- 调用安全任务查询接口

### 5. 响应处理
```java
private void handleSecurityResponse(JSONObject response, String originalCode, String taskId) {
    String retCode = response.optString("retCode");
    
    if (REQUEST_SUCCESS.equals(retCode)) {
        JSONObject data = response.optJSONObject("data");
        int taskState = data.optInt("taskState", -1);
        
        if (taskState == SECURITY_NORMAL || taskState == SECURITY_PAUSE) {
            // 跳转到安全迁移页面
            navigateToSecurityPage(SECURITY_API_URL, data.toString());
        } else {
            showErrorToast(ERROR_TASK_INFO);
        }
    } else if (REQUEST_TASK_NO_EXIST.equals(retCode)) {
        showErrorToast(ERROR_TASK_INFO);
    } else if (REQUEST_OVER_MAX.equals(retCode)) {
        showErrorToast(ERROR_TASK_OVER);
    }
}
```

## 错误处理

### 错误类型对应关系

| Flutter错误信息 | Android错误信息 | 触发条件 |
|----------------|----------------|----------|
| `QR_NO_NETWORK_ERROR` | `ERROR_NO_NETWORK` | 网络不可用 |
| `QR_NOT_LOGIN_PROMPT` | `ERROR_NOT_LOGIN` | 用户未登录 |
| `QR_NO_PARSE_PROMPT` | `ERROR_NO_PARSE` | 任务ID解析失败 |
| `QR_TASK_INFO_ERROR` | `ERROR_TASK_INFO` | 任务不存在或其他错误 |
| `QR_TASK_OVER_ERROR` | `ERROR_TASK_OVER` | 超过设备绑定数量限制 |

### 任务状态处理

| 状态值 | 状态名称 | 处理方式 |
|-------|---------|----------|
| 0 | SECURITY_NORMAL (待交割) | 允许跳转 |
| 1 | SECURITY_DONE (已完成) | 显示错误 |
| 2 | SECURITY_CANCEL (已取消) | 显示错误 |
| 3 | SECURITY_PAUSE (已锁定) | 允许跳转 |

## 常量定义

### API相关
```java
private static final String SECURITY_CODE_MARK = "MIGRATE_QR$";
private static final String SECURITY_API_URL = "/api-gw/wisdomdevice/device/task/anonymous/query";
```

### 响应码
```java
private static final String REQUEST_SUCCESS = "00000";
private static final String REQUEST_TASK_NO_EXIST = "1080012";
private static final String REQUEST_OVER_MAX = "1080020";
```

### 任务状态
```java
private static final int SECURITY_NORMAL = 0;
private static final int SECURITY_DONE = 1;
private static final int SECURITY_CANCEL = 2;
private static final int SECURITY_PAUSE = 3;
```

## 待完善功能

以下功能使用伪代码实现，需要在实际项目中替换为真实实现：

1. **网络状态检查** (`checkNetworkStatus()`)
2. **登录状态检查** (`checkLoginStatus()`)
3. **网络请求** (`simulateNetworkRequest()`)
4. **Toast显示** (`showErrorToast()`)
5. **页面跳转** (`navigateToSecurityPage()`)

## 使用示例

```java
// 创建Handler实例
SecurityCodeHandler securityHandler = new SecurityCodeHandler();

// 设置控制接口
securityHandler.setUpScanControlDelegate(scanControlDelegate);

// 处理扫码结果
securityHandler.handle("MIGRATE_QR$12345$abcdef", UpScanResultSource.CAMERA);
```

## 注意事项

1. **扫码控制**：处理过程中会暂停扫码，处理完成后恢复扫码
2. **错误处理**：所有错误情况都会显示对应的错误提示并恢复扫码
3. **线程安全**：网络请求应在后台线程执行，UI操作需切换到主线程
4. **内存管理**：使用弱引用避免内存泄漏，包含Activity生命周期检查
