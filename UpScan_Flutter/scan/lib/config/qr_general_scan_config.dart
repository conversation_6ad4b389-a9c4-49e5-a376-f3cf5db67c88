class QRScanParametersConfig {
  final String? scanTitle;
  final String? scanContent;
  final String? scanError;
  final String? scanRules;
  final String? btn1_Title;
  final String? btn1_Link;
  final String? btn2_Title;
  final String? btn2_Link;
  final bool? isShowAlbum;
  final String highLightContent;
  final String showDefaultIcon;

  const QRScanParametersConfig({
    this.scanTitle,
    this.scanContent,
    this.scanError,
    this.scanRules,
    this.btn1_Title,
    this.btn1_Link,
    this.btn2_Title,
    this.btn2_Link,
    this.isShowAlbum,
    this.highLightContent = '',
    this.showDefaultIcon = 'false', // true 表示底部显示手电筒、相册；false 表示底部按钮根据配置显示
  });
}
