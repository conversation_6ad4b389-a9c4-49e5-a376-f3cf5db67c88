import 'dart:ui';
import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:log/log.dart';
import 'package:log/log_modle.dart';
import 'package:r_scan/r_scan.dart';
import 'package:scan/config/constant.dart';
import 'package:scan/util/qr_scan_common_util.dart';
import 'package:scan/util/qr_scan_string_util.dart';
import 'package:scan/util/white_code_util.dart';
import 'package:vdn/back.dart';
import '../model/qr_scan_album_model.dart';
import '../model/qr_scan_query_permission_model.dart';
import '../util/common_util.dart';
import 'package:uppermission/requestresult.dart';
import 'qr_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vdn/vdn.dart';
import 'package:scan/config/qr_sting.dart';
import 'package:photo/photo.dart';
import 'package:scan/common_widget/qr_scan_toast.dart';
import 'package:message/message.dart';
import 'package:message/msgmodel.dart';
import 'package:uppermission/uppermission.dart';
import '../util/qr_log.dart';
import 'package:flutter/src/widgets/routes.dart';

typedef QRSYNSBackFunction = void Function(Map result);
typedef QRSYNScanHandler = void Function(
    List<RScanResult> result, List? whiteList, VoidCallback popAction);

List<RScanCameraDescription>? rScanCameras;

class RScanSYNCameraDialog extends StatefulWidget {
  final String description;
  final bool showPhoto;

  @required
  final QRSYNScanHandler scanResult;

  RScanSYNCameraDialog(
      {this.description = '', this.showPhoto = true, required this.scanResult});

  @override
  _RScanSYNCameraDialogState createState() =>
      _RScanSYNCameraDialogState(description, showPhoto, scanResult);
}

class _RScanSYNCameraDialogState extends State<RScanSYNCameraDialog>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  RScanCameraController? _controller;
  bool isFirst = true;
  String _contentDes;
  bool showPhoto;
  Timer? _timer;
  StreamSubscription? _resumeMessage;
  StreamSubscription? _pauseMessage;
  StreamController? _streamController;
  bool _flashMode = false;
  List? _whiteList;
  // 是否正在识别相册的图片
  bool _isAlbumScan = false;
  AnimationController? _animationController;

  //    扫一扫的线是否需要动画
  bool _isAnimatedLine = true;

  QRSYNScanHandler scanResult;

  // 是否展示相机设置提示
  bool _cameraTipShow = false;

  _RScanSYNCameraDialogState(this._contentDes, this.showPhoto, this.scanResult);

  String _gainDefaultContentDes() {
    return QRContentString.QR_SCAN_CONTENT;
  }

  void initCamera() {
    if (rScanCameras == null || rScanCameras!.length == 0) {
      _requestPermissionFunction('camera', allowedCallback: () {
        _reloadScanContext();
      });
    }
    _initCameraController();
  }

  void _initCameraController() async {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': '_initCameraController', 'rScanCameras': rScanCameras});
    if (rScanCameras != null && rScanCameras!.length > 0) {
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': '_initCameraController', 'info': 'inter'});

      _controller = RScanCameraController(
          rScanCameras![0], RScanCameraResolutionPreset.high)
        ..addListener(() {
          LogHelper.debug(
              tag: QRScanLogString.tagQRScan,
              msg: {'fn': '_initCameraController', 'info': 'begian scan'});
          final result = _controller!.result;
          LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: {
            'fn': '_initCameraController',
            'result': result,
            '_controller': _controller,
            'isFirst': isFirst
          });
          if (result != null) {
            if (isFirst) {
              LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: {
                'fn': '_initCameraController',
                'scanResult': scanResult
              });
              scanResult(result, _whiteList, () {
                _disposeEvent();
                Vdn.close(result: {'data': result.first.message});
              });
              isFirst = false;
              //重置扫码
              resetScan();
            }
          }
        })
        ..initialize().then((_) {
          if (!mounted) {
            return;
          }
          setState(() {});
        });
      _flashMode = (await _controller?.getFlashMode())!;
    }
  }

  void resetScan() {
    if (_timer == null) {
      _timer = Timer.periodic(Duration(milliseconds: 3000), (t) {
        isFirst = true;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    BackKeyManager.disallowSystemBack();
    BackKeyManager.registerEventChannel((event) {
      Log.printLog(LogModle('debug', '[scan]', 'scan backPress'));
      _backAction();
    });
    WidgetsBinding.instance.addObserver(this);
    _streamController = StreamController<bool>();
    WhiteCodeUtils.getInstance()
        .gainQrWhiteCodeFromResource(CONSTANT.synWhiteCode);
    WhiteCodeUtils.getInstance()
        .gainQrWhiteCode(CONSTANT.synWhiteCode)
        .then((value) {
      _whiteList = value;
    });
    initCamera();
    //监听app进入前后台事件
    setResumeOrPauseEvent();

    //初始化动画，用于相册识别页面展示效果
    _animationController = AnimationController(
        duration: Duration(milliseconds: 1500), vsync: this);
    if (Platform.isAndroid) {
      RScan.getInstance().init().setCallBack(_doScanImgFromAndroid);
    }
  }

  void setResumeOrPauseEvent() {
    _resumeMessage = Message.listen<AppResumeMessage>((event) async {
      if (_controller != null && _flashMode == true) {
        _flashMode = false;
        _controller!.setFlashMode(_flashMode);
        _streamController?.add(_flashMode);
      }

      //从后台返回前台时，重新初始化
      if (Platform.isAndroid) {
        bool granted = await getCameraPermission();
        if (granted && _controller == null) {
          _initCameraController();
        }
      }
    });

    _pauseMessage = Message.listen<AppPauseMessage>((event) async {
      if (Platform.isAndroid) {
        bool granted = await getCameraPermission();
        if (granted && _controller != null) {
          _controller!.dispose();
          _controller = null;
        }
      }
    });
  }

  Future<bool> getCameraPermission() async {
    try {
      List<String> permissions = [];
      permissions.add('camera');
      List<dynamic> resultList =
          await Uppermission.queryPermission(permissions);
      final Map<String, dynamic> resultMap =
          CommonUtil.convertType<Map<dynamic, dynamic>>(
              resultList[0], <dynamic, dynamic>{}).cast<String, dynamic>();
      final QueryPermissionResult permissionResult =
          QueryPermissionResult.fromJson(resultMap);
      return '${permissionResult.granted}' == 'true';
    } catch (e) {
      return false;
    }
  }

  void _updateCameraTipShowState(bool show) {
    LogHelper.info(
        tag: QRScanLogString.tagQRScan,
        msg: 'updateCameraTipShowState, start with show = ${show}');
    if (show == _cameraTipShow || !mounted) {
      LogHelper.info(tag: QRScanLogString.tagQRScan, msg: {
        'updateCameraTipShowState err, return with show':
            '${show == _cameraTipShow} !mounted = ${!mounted}',
        'info': '${!mounted}'
      });
      return;
    }
    setState(() {
      _cameraTipShow = show;
    });
  }

  // @override
  // void didChangeDependencies() {
  //   super.didChangeDependencies();
  //   WidgetsBinding.instance.addObserver(this);
  // }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    LogHelper.info(
        tag: QRScanLogString.tagQRScan,
        msg: 'syn scan---didChangeAppLifecycleState$state.');
    switch(state) {
      case AppLifecycleState.resumed:
        onPageShow();
        break;
      case AppLifecycleState.hidden:
        onPageHide();
        break;
      case AppLifecycleState.detached:
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
        break;
    }
  }

  void onPageShow() async {
    final bool granted = await getCameraPermission();
    if (granted) {
      _updateCameraTipShowState(false);
      if (_controller == null) {
        LogHelper.info(
            tag: QRScanLogString.tagQRScan,
            msg: 'scan onPageShow, with controller null.');
        if (rScanCameras == null || rScanCameras!.isEmpty) {
          _reloadScanContext();
        }
        _initCameraController();
      }
    } else {
      _updateCameraTipShowState(true);
    }
    if (_controller != null) {
      if (!_isAlbumScan) {
        //没在识别相册中的图片，开始调用mPaaS
        _controller!.startGetScanResult();
      }
      bool flashModel = (await _controller!.getFlashMode())!;
      _flashMode = flashModel;
      _streamController?.add(_flashMode);
    }
  }

  @override
  void onPageHide() {
    if (_controller != null) {
      _controller!.stopGetScanResult();
      if (_flashMode != false) {
        _flashMode = false;
        _controller!.setFlashMode(_flashMode);
        _streamController?.add(_flashMode);
      }
    }
  }

  void _disposeEvent() {
    if (Platform.isAndroid) {
      if (!mounted) {
        return;
      }
      // 处理Android 扫码页面内存泄漏问题; ios 端在native侧处理
      _controller?.removeListener(() {});
      _controller?.dispose();
      _timer?.cancel();
      _resumeMessage?.cancel();
      _streamController?.close();
      _pauseMessage?.cancel();
      _animationController?.dispose();
      if (Platform.isAndroid) {
        RScan.getInstance().destroy();
      }
      WidgetsBinding.instance.removeObserver(this);
    }
  }

  @override
  void dispose() {
    super.dispose();
    _disposeEvent();
  }

  @override
  Widget build(BuildContext context) {
    QRScanCommonUtil().context = context;
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    //此处计算是根据裁剪框的大小设定的
    ScreenUtil.init(
        BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width,
            maxHeight: MediaQuery.of(context).size.height),
        designSize: width > height ? Size(240, 812) : Size(375, 812),
        orientation: Orientation.portrait);
    double boxWidth = ScreenUtil().screenWidth * (width > height ? 0.35 : 0.55);
    //加载中. 加载中.. 加载中... 循环显示 间隔500ms
    Tween(begin: 0.0, end: 1.0)
        .chain(CurveTween(curve: Interval(0, 1 / 3)))
        .animate(_animationController!);
    Tween(begin: 0.0, end: 1.0)
        .chain(CurveTween(curve: Interval(1 / 3, 2 / 3)))
        .animate(_animationController!);
    Tween(begin: 0.0, end: 1.0)
        .chain(CurveTween(curve: Interval(2 / 3, 1)))
        .animate(_animationController!);
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: <Widget>[
          _gainContainerWidget(boxWidth),
          Positioned(
            top: 90 +
                MediaQueryData.fromWindow(window).padding.top +
                40 +
                MediaQueryData.fromWindow(window).size.width * 2 / 3 / 2 -
                10,
            left: MediaQueryData.fromWindow(window).size.width / 2 - 50,
            child: Offstage(
              offstage: !_isAlbumScan,
              child: AnimatedBuilder(
                animation: _animationController!,
                builder: ((BuildContext context, Widget? child) {
                  return Container(
                    height: 20,
                    constraints: BoxConstraints(minWidth: 100),
                    alignment: Alignment.center,
                    child: Text(
                      _animationController!.value <= 1 / 3
                          ? '加载中.'
                          : (_animationController!.value >= 2 / 3
                              ? '加载中...'
                              : '加载中..'),
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  );
                }),
              ),
            ),
          ),
          SingleChildScrollView(
            child: Column(
              children: <Widget>[
                Container(
                  height: MediaQueryData.fromWindow(window).padding.top + 90,
                  child: AppBarWidget(
                    showPhoto: widget.showPhoto,
                    leftClick: _throttle(() async {
                      _backAction();
                      LogHelper.debug(
                          tag: QRScanLogString.tagQRScan,
                          msg: {'fn': 'build', 'info': '_backAction'});
                      await Future.delayed(Duration(milliseconds: 2000));
                    }) as Function()?,
                    // _backAction,
                    photoAction: _throttle(() async {
                      _photoAction();
                      await Future.delayed(Duration(milliseconds: 2000));
                    }) as Function()?,
                  ),
                ),
                Stack(
                  children: [
                    SizedBox(
                      height: boxWidth + 15 + 40,
                    ),
                    if (_cameraTipShow == true)
                      Container(
                        width: boxWidth,
                        height: boxWidth,
                        margin: const EdgeInsets.only(top: 40, bottom: 15),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              margin: const EdgeInsets.only(left: 8, right: 8),
                              child: const Text(
                                  CameraPermissionConfig
                                      .QR_SCAN_CAMERA_PERMISSION_TIPS,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      fontSize: 14, color: Color(0xFFFFFFFF))),
                            ),
                            const SizedBox(
                              height: 12,
                            ),
                            GestureDetector(
                              onTap: () {
                                // 跳转设置
                                Vdn.goToPage(CameraPermissionConfig
                                    .QR_SCAN_CAMERA_PERMISSION_CONFIG_URL);
                              },
                              child: Container(
                                width: 114,
                                height: 36,
                                decoration: BoxDecoration(
                                  color: const Color(0xFFBE965A),
                                  border: Border.all(
                                      color: const Color(0xFFBE965A)),
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(18)),
                                ),
                                child: const Center(
                                  child: Text(
                                      CameraPermissionConfig
                                          .QR_SCAN_CAMERA_PERMISSION_OPEN,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: Color(0xFFFFFFFF))),
                                ),
                              ),
                            )
                          ],
                        ),
                      )
                    else
                      Container(),
                  ],
                ),
                Text(
                  StringUtil.isBlankString(_contentDes)
                      ? _gainDefaultContentDes()
                      : _contentDes,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: 65,
                ),
                Container(
                  height: 81,
                  child: AttachmentWidget(
                    flashMode: _flashMode,
                    streamController: _streamController!,
                    flashButton: _buildFlashBtn,
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _gainContainerWidget(double scanBoxWidth) {
    Widget tmp;
    bool needAnimate = false;
    if (rScanCameras == null || rScanCameras!.length == 0) {
      tmp = Container(
        color: Colors.black,
        alignment: Alignment.center,
        child: Text(
          '',
          //QRContentString.QR_NO_PHOTO_PERMISSIONS,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.0,
          ),
        ),
      );
    } else if (_controller == null || !_controller!.value.isInitialized!) {
      tmp = Container(
        color: Colors.black,
      );
    } else {
      double scaleValue = 1.3;
      try {
        double sizeRatio = ScreenUtil().uiSize.aspectRatio; //0.46
        double aspectRatio = _controller!.value.aspectRatio; //0.5625
        String scale = (aspectRatio / sizeRatio).toStringAsFixed(1);
        scaleValue = double.parse(scale) + 0.1;
      } catch (e) {
        LogHelper.debug(
            tag: QRScanLogString.tagQRScan,
            msg: {'catch': 'scalError', 'result': scaleValue});
      }
      tmp = Container(
        alignment: Alignment.center,
        child: Transform.scale(
          scale: scaleValue,
          child: AspectRatio(
            aspectRatio: _controller!.value.aspectRatio,
            child: RScanCamera(_controller!),
          ),
        ),
      );
      needAnimate = true;
    }
    return ScanImageView(
      boxWidth: scanBoxWidth,
      child: tmp,
      needAnimate: needAnimate && !_isAlbumScan && _isAnimatedLine,
    );
  }

  /// 函数节流
  ///
  /// [func]: 要执行的方法
  Function _throttle(
    Future Function() func,
  ) {
    bool enable = true;
    Function target = () {
      if (enable == true) {
        enable = false;
        func().then((_) {
          enable = true;
        });
      }
    };
    return target;
  }

  void _backAction() {
    _disposeEvent();
    Vdn.close();
  }

  void _photoAction() {
    setState(() {
      _isAnimatedLine = false;
    });
    _openAlbum();
  }

  void _openAlbum() {
    const int maxSelected = 1;
    const bool isFromScan = true;
    const bool originalImage = true;
    Vdn.goToPage('flutter://photocheck'
        , params: <String, String>{'max': '$maxSelected',
          'isFromScan': '$isFromScan',
          'showOriginalBtn': '$originalImage'
        })
        .then((Map mapValue) async {
      Log.printLog(LogModle(
          'debug', '[scan]', 'synscan-openPhotoPicker $mapValue'));
      if (mapValue['data'] is List?) {
        List? value = mapValue['data'] as List?;
        if (value != null && value.length > 0) {
          if (value[0] is! Map) {
            setState(() {
              _isAnimatedLine = true;
            });
            return;
          }
          final Map<String, dynamic> pathMap =
          CommonUtil.convertType<Map<dynamic, dynamic>>(
              value[0], <dynamic, dynamic>{}).cast<String, dynamic>();
          final QrAlbumResultData albumResultData =
          QrAlbumResultData.fromJson(pathMap);
          final String? path = albumResultData.path;
          if (!StringUtil.isBlankString(path)) {
            _animationController!.repeat();
            setState(() {
              _isAlbumScan = true;
            });
            if (Platform.isIOS) {
              final result = await RScan.scanImagePath(path!);
              _animationController!.stop();
              setState(() {
                _isAlbumScan = false;
              });
              if (result == null) {
                setState(() {
                  _isAnimatedLine = true;
                });
                QRScanToast()
                    .showErrorText(QRScanErrorString.QR_NO_SCAN_RESULT);
              } else {
                _closeRotue(result);
              }
              if (_controller != null) {
                _controller!.startGetScanResult();
              }
            } else if (Platform.isAndroid) {
              RScan.getInstance().scanImagePathFromAndroid(path!);
            }
          } else {
            setState(() {
              _isAnimatedLine = true;
            });
          }
        } else {
          setState(() {
            _isAnimatedLine = true;
          });
        }
      }
    });
  }

  /// 针对安卓相册图片扫码回调函数
  void _doScanImgFromAndroid(List<RScanResult>? result) {
    if (_animationController != null) {
      _animationController!.stop();
    }
    setState(() {
      _isAlbumScan = false;
    });
    if (result == null || result.isEmpty) {
      setState(() {
        _isAnimatedLine = true;
      });
      QRScanToast().showErrorText(QRScanErrorString.QR_NO_SCAN_RESULT);
    } else {
      _closeRotue(result);
    }
    if (_controller != null) {
      _controller!.startGetScanResult();
    }
  }

  void _requestPermissionFunction(String permissionName,
      {VoidCallback? allowedCallback, VoidCallback? cancelCallback}) async {
    try {
      final RequestResult result = await Uppermission.requestPermission(
          [permissionName],
          immediate: false);
      if (result.isAllowed == false) {
        _updateCameraTipShowState(true);
        if (cancelCallback != null) {
          cancelCallback();
        }
      } else {
        _updateCameraTipShowState(false);
        if (allowedCallback != null) {
          allowedCallback();
        }
      }
    } catch (e) {
      _updateCameraTipShowState(false);
      LogHelper.debug(
          tag: QRScanLogString.tagQRScan,
          msg: {'fn': '_requestPermissionFunction', 'catchError': e});
    }
  }

  void _reloadScanContext() async {
    rScanCameras = await availableRScanCameras();
    _initCameraController();
    //此处这么处理是为了等_controller 初始化完成
    Timer.periodic(Duration(milliseconds: 200), (t) {
      if (_controller != null) {
        _controller!.startGetScanResult();
      }
      t.cancel();
    });
  }

  void _closeRotue(List<RScanResult> result) async {
    ///延时0.3秒，为了让当前vdn能够退出
    await Future.delayed(Duration(milliseconds: 500), () {
      scanResult(result, _whiteList, () {
        _disposeEvent();
        Vdn.close(result: {'data': result.first.message});
      });
    });
    setState(() {
      _isAnimatedLine = true;
    });
  }

  Widget _buildFlashBtn(BuildContext context, AsyncSnapshot<bool> snapshot) {
    return snapshot.hasData
        ? IconButton(
            icon: snapshot.data!
                ? Image.asset(
                    'images/upscan_torch_selected.png',
                    package: 'scan',
                  )
                : Image.asset(
                    'images/upscan_torch_default.png',
                    package: 'scan',
                  ),
            color: Colors.white,
            iconSize: 46,
            onPressed: flashButtonPressed,
          )
        : Container();
  }

  void flashButtonPressed() {
    _requestPermissionFunction('camera', allowedCallback: () {
      if (_controller != null) {
        _flashMode = !_flashMode;
        _controller!.setFlashMode(_flashMode);
        _streamController?.add(_flashMode);
      } else {
        // 如果权限已同意，且摄像头未初始化，此时初始化摄像头
        if (Platform.isAndroid) {
          _reloadScanContext();
        }
      }
    });
  }
}

class AppBarWidget extends StatelessWidget {
  final bool showPhoto;
  final VoidCallback? leftClick;
  final VoidCallback? photoAction;

  AppBarWidget({this.leftClick, this.photoAction, this.showPhoto = true});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 20),
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            BackButton(
              onPressed: leftClick,
              color: Colors.white,
            ),
            Text(
              QRContentString.QR_SCAN,
              style: TextStyle(
                fontSize: 17,
                color: Colors.white,
              ),
            ),
            showPhoto
                ? IconButton(
                    icon: Image.asset(
                      'images/album.png',
                      package: 'scan',
                    ),
                    color: Colors.white,
                    iconSize: 46,
                    onPressed: photoAction)
                : Container(width: 46.w),
          ]),
    );
  }
}

class AttachmentWidget extends StatefulWidget {
  final VoidCallback? torchAction;
  final StreamController streamController;
  @required
  final bool flashMode;
  @required
  final Widget Function(BuildContext, AsyncSnapshot<bool>) flashButton;

  AttachmentWidget(
      {required this.flashMode,
      required this.flashButton,
      this.torchAction,
      required this.streamController});

  _AttachmentWidgetState createState() => _AttachmentWidgetState();
}

class _AttachmentWidgetState extends State<AttachmentWidget> {
  @override
  Widget build(BuildContext context) {
    return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: <Widget>[
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              StreamBuilder<bool>(
                initialData: widget.flashMode,
                stream: widget.streamController.stream as Stream<bool>,
                builder: widget.flashButton,
              ),
              // Text(
              //   QRContentString.QR_SCAN_TORCH,
              //   style: TextStyle(
              //     fontSize: 13,
              //     color: Colors.white,
              //   ),
              // ),
            ],
          ),
        ]);
  }
}
