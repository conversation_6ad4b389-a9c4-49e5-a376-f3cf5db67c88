import 'package:flutter/material.dart';

class QRScanCommonUtil {

  BuildContext? context;

  // 工厂模式
  factory QRScanCommonUtil() =>_getInstance();
  static QRScanCommonUtil get instance => _getInstance();
  static QRScanCommonUtil? _instance;
  QRScanCommonUtil._internal() {
    // 初始化
  }
  static QRScanCommonUtil _getInstance() {
    if (_instance == null) {
      _instance = new QRScanCommonUtil._internal();
    }
    return _instance!;
  }
}