import '../config/qr_sting.dart';
import '../util/common_util.dart';
import '../util/qr_log.dart';

class QueryPermissionResult {
  /// fromJson
  QueryPermissionResult.fromJson(Map<String, dynamic> json) {
    LogHelper.debug(
        tag: QRScanLogString.tagQRScan,
        msg: {'fn': 'QrScanQueryPermissionResult from json', 'data': '$json'});
    if (json.containsKey('granted')) {
      granted = CommonUtil.convertType<bool?>(json['granted'], null);
    }
  }

  /// granted
  bool? granted;
}
