import '../util/common_util.dart';

class QRCodeInfoModel {
  QRCodeInfoModel({
    required this.familyId,
    required this.familyName,
    required this.inviteUserName,
    required this.inviteUserId,
    required this.inviteAvatarUrl,
  });

  QRCodeInfoModel.fromJson(Map<String, dynamic> json) {
    familyId = CommonUtil.convertType<String>(json['familyId'], '');
    familyName = CommonUtil.convertType<String>(json['familyName'], '');
    inviteUserName = CommonUtil.convertType<String>(json['inviteUserName'], '');
    inviteUserId = CommonUtil.convertType<String>(json['inviteUserId'], '');
    inviteAvatarUrl =
        CommonUtil.convertType<String>(json['inviteAvatarUrl'], '');
  }

  String familyId = '';
  String familyName = '';
  String inviteUserName = '';
  String inviteUserId = '';
  String inviteAvatarUrl = '';

  Map<String, String> toJson() {
    final Map<String, String> data = <String, String>{};
    data['familyId'] = familyId;
    data['familyName'] = familyName;
    data['inviteUserName'] = inviteUserName;
    data['inviteUserId'] = inviteUserId;
    data['inviteAvatarUrl'] = inviteAvatarUrl;
    return data;
  }

  @override
  String toString() {
    return 'QRCodeInfoModel{familyId: $familyId, familyName: $familyName, inviteUserName: $inviteUserName, inviteUserId: $inviteUserId, inviteAvatarUrl: $inviteAvatarUrl}';
  }
}
