import 'package:r_scan/r_scan.dart';
import 'package:scan/config/qr_general_scan_config.dart';
import 'package:scan/common_widget/qr_scan_toast.dart';
import 'package:scan/util/qr_scan_string_util.dart';
import 'package:scan/config/qr_sting.dart';

import '../util/qr_log.dart';

typedef QRGeneralScanBackFunction = void Function(Map<dynamic, dynamic> result);

class QRGeneralScanHandler {
  void handleScanResult(List<RScanResult>? resultList,
      QRScanParametersConfig config, QRGeneralScanBackFunction popAction) {
    if (resultList == null || resultList.isEmpty) {
      QRScanToast().showErrorText(QRScanErrorString.QR_NO_PARSE_PROMPT);
      return;
    }
    LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: <String, dynamic>{
      'fn': 'QRGeneralScanHandler - handleScanResult',
      'result': resultList,
    });

    if (!StringUtil.isBlankString(config.scanRules) &&
        !StringUtil.isBlankString(config.scanError)) {
      for (final RScanResult scanResult in resultList) {
        final String str = scanResult.message!;
        if (RegExp(config.scanRules!).hasMatch(str)) {
          popAction(<String, dynamic>{'result': str});
          break;
        } else {
          QRScanToast().showErrorText(config.scanError!);
        }
      }
    } else {
      popAction(<String, dynamic>{'result': resultList});
    }
  }
}
