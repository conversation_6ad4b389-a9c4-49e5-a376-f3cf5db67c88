import 'dart:convert';
import 'dart:typed_data';
import 'package:Appinfos/Appinfos.dart';
import 'package:Appinfos/AppinfosModel.dart';
import 'package:crypto/crypto.dart';
import 'package:user/user.dart';

enum METHOD { GET, POST }

// copy appmine代码
class HTTP {
  // hash string
  static const HEX_STRING = '0123456789abcdef';

  // 获取时间戳
  static int getTimestamp() {
    return new DateTime.now().millisecondsSinceEpoch;
  }

  // 设置请求头
  static Map<String, dynamic> commonHeaders() {
    return {
      'Content-Type': 'application/json;charset=UTF-8',
      'timestamp': getTimestamp().toString(),
      'sequenceId': getTimestamp().toString(),
    };
  }

  static String sign(
    METHOD method,
    String urlPath,
    Map<String, dynamic> params,
    String appId,
    String appKey,
    int timestamp,
  ) {
    String paramString = '';
    if (method == METHOD.POST) {
      paramString = json.encode(params);
    } else {
      if (params is Map && params.keys.length > 0) {
        if (!urlPath.contains('?')) {
          paramString = '?';
        }
        for (String key in params.keys) {
          paramString = paramString + '$key=${params[key]}&';
        }
        if (paramString.contains('&')) {
          paramString = paramString.substring(0, paramString.length - 1);
        }
      }
    }
    return getSign(urlPath, paramString, appId, appKey, timestamp);
  }

  /// 获取请求签名
  /// [urlPath] 请求url路径
  /// [paramsJson] 请求参数Json字符串(请求body)
  /// [appId] appId
  /// [appKey] appKey
  /// [timestamp] 时间戳（毫秒）
  static String getSign(
    String urlPath,
    String paramsJson,
    String appId,
    String appKey,
    int timestamp,
  ) {
    var bytes = utf8
        .encode(urlPath + paramsJson + appId + appKey + timestamp.toString());
    var list = sha256.convert(bytes).bytes;
    if (list == null || list.length <= 0) {
      return '';
    }
    int length = list.length;
    Uint8List uList = new Uint8List(length << 1);
    int i = 0;
    for (int j = 0; j < length; j++) {
      int k = i + 1;
      final index = (list[j] >> 4) & 0xF;
      uList[i] = HEX_STRING[index].codeUnitAt(0);
      uList[k] = HEX_STRING[list[j] & 0xF].codeUnitAt(0);
      i = k + 1;
    }
    return String.fromCharCodes(uList);
  }

  /// 获取请求签名
  /// [urlPath] 请求url路径
  /// [paramsJson] 请求参数Json字符串(请求body)
  /// [appId] appId
  /// [appKey] appKey
  /// [timestamp] 时间戳（毫秒）
  static String getMD5Sign(
    String urlPath,
    String paramsJson,
    String appId,
    String appKey,
    int timestamp,
  ) {
    var bytes = utf8
        .encode(urlPath + paramsJson + appId + appKey + timestamp.toString());
    var list = md5.convert(bytes).bytes;
    if (list == null || list.length <= 0) {
      return '';
    }
    int length = list.length;
    Uint8List uList = new Uint8List(length << 1);
    int i = 0;
    for (int j = 0; j < length; j++) {
      int k = i + 1;
      final index = (list[j] >> 4) & 0xF;
      uList[i] = HEX_STRING[index].codeUnitAt(0);
      uList[k] = HEX_STRING[list[j] & 0xF].codeUnitAt(0);
      i = k + 1;
    }
    return String.fromCharCodes(uList);
  }

  static Future<Map<String, dynamic>> headers(
      [Map<String, dynamic>? otherHeaderInfo, timestamp]) async {
    String _accessToken;
    String _accountToken;
    String _userId;
    try {
      final _oauthdata = await User.getOauthData();
      final _userinfo = await User.getUserInfo();
      _accessToken = _oauthdata.uhome_access_token;
      _accountToken = _oauthdata.user_center_access_token;
      _userId = _userinfo.userId;
      AppInfoModel appInfoModel = await AppInfoPlugin.getAppInfo();

      Map<String, dynamic> headers = {
        'timestamp': timestamp.toString(),
        'appId': appInfoModel.appId,
        'appKey': appInfoModel.appKey,
        'clientId': appInfoModel.clientId,
        'accessToken': _accessToken,
        'accountToken': _accountToken,
        'appVersion': appInfoModel.appVersion,
      };

      if (otherHeaderInfo != null) {
        headers.addAll(otherHeaderInfo);
      }
      return headers;
    } catch (e) {
      return new Map<String, dynamic>();
    }
  }
}
