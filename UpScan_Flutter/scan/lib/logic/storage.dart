import 'dart:io';
import 'dart:async';
import 'dart:convert' as convert;
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:scan/config/constant.dart';
import 'package:scan/util/qr_log.dart';
import 'package:scan/config/qr_sting.dart';

class QRStorage {
  Future<String> get _localPath async {
    final _path = await getApplicationDocumentsDirectory();
    return _path.path;
  }

  //动态化组件数据
  Future<File>  _qrWhiteCodeFile(String whiteCode) async {
    final path = await _localPath;
    if(whiteCode==CONSTANT.synWhiteCode){
      return File('$path/qrwhitecodesyn.json');
    }
    return File('$path/qrwhitecode.json');
  }

  Future<File> saveQrWhiteCodeJson(String strURL,String whiteCode) async{
    final http.Response responseData = await http.get(Uri.parse(strURL));
    Uint8List uint8list = responseData.bodyBytes;
    var buffer = uint8list.buffer;
    ByteData byteData = ByteData.view(buffer);
    final file = await _qrWhiteCodeFile(whiteCode);
    return file.writeAsBytes(
        buffer.asUint8List(byteData.offsetInBytes, byteData.lengthInBytes));
  }

  Future<List?> readQrWhiteCodeData(String whiteCode) async {
    try {
      final file = await _qrWhiteCodeFile(whiteCode);
      var dir_bool = await file.exists();
      if(dir_bool == false){
        return null;
      }
      var jsonStr = await file.readAsString();
      Map data = convert.jsonDecode(jsonStr) as Map;
      if (data is! Map ) {
        return null;
      }
      List realData = data['whiteList'] as List;
      LogHelper.debug(tag: QRScanLogString.tagQRScan, msg: {'fn': 'readQrWhiteCodeData', whiteCode: realData});
      if (realData is List && realData.length > 0){
        return realData;
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}