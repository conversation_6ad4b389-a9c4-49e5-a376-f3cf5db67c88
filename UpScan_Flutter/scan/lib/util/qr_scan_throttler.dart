import 'dart:async';

class Throttler {
  Throttler({required this.milliseconds});

  int milliseconds;
  Timer? _timer;
  bool _isExecuted = false;

  void run(void Function() action, {void Function()? multiClickCallback}) {
    if (_isExecuted) {
      multiClickCallback?.call();
      return;
    }
    _timer = Timer(Duration(milliseconds: milliseconds), () {
      _timer?.cancel();
      _isExecuted = false;
    });
    _isExecuted = true;
    action();
  }

  void dispose() {
    _timer?.cancel();
  }
}

class QRScanThrottle {
  factory QRScanThrottle() => _instance;
  QRScanThrottle._();

  static final QRScanThrottle _instance = QRScanThrottle._();

  final Throttler _throttler = Throttler(milliseconds: 1000);

  Throttler get throttler => _throttler;

  void dispose() {
    _throttler.dispose();
  }
}
