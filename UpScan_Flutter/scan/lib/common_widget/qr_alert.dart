import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

bool isFamilyPrivacyAgreementAlertShowing = false;

class QRAlert extends StatefulWidget{
  final String title;
  final String content;
  final String additionalContent;
  final String sureTitle;
  final String cancelTitle;
  final VoidCallback sureAction;
  final VoidCallback cancelAction;
  const QRAlert(this.title,this.content,this.additionalContent, this.sureTitle,this.cancelTitle,this.sureAction,this.cancelAction);
  @override
  _QRAlertState createState() => _QRAlertState();
}
class _QRAlertState extends State<QRAlert> {
  @override
  Widget build(BuildContext context) {
    
    return CupertinoAlertDialog(
          title: Text(widget.title),
          content: SingleChildScrollView(
            child: ListBody(
              children: [
                Text(
                  widget.content,
                  textAlign: TextAlign.left,
                ),
                Text(
                  widget.additionalContent,
                  textAlign: TextAlign.left,
                ),
              ],
            )
          ),
          actions: [
            CupertinoDialogAction(
                child: Text(
                  widget.cancelTitle,
                  style: TextStyle(
                    color: Color(0x66666666)
                  ),
                ),
                onPressed: widget.cancelAction,
              ),
              CupertinoDialogAction(
                child: Text(widget.sureTitle),
                onPressed: widget.sureAction,
              ),
          ],
        );
  }

  @override
  void dispose() {
    super.dispose();
    isFamilyPrivacyAgreementAlertShowing = false;
  }
}