import 'package:flutter/material.dart';
// import 'package:permission_handler/permission_handler.dart';

class NoPermissionDialog extends StatefulWidget {
  final PermissionModel dialogModel;

  const NoPermissionDialog(this.dialogModel);

  @override
  _NoPermissionDialogState createState() => _NoPermissionDialogState();
}

class _NoPermissionDialogState extends State<NoPermissionDialog> {
  @override
  Widget build(BuildContext context) {
    var provider = widget.dialogModel;
    return AlertDialog(
      title: Text(
        provider.titleText!,
        style: TextStyle(
          color: Colors.black,
          fontSize: 16.0, 
        ),
      ),
      actions: <Widget>[
        TextButton(
          onPressed: _onCancel,
          child: Text(provider.cancelText!),
        ),
        TextButton(
          onPressed: _onSure,
          child: Text(provider.sureText!),
        ),
      ],
    );
  }

  void _onCancel() {
    Navigator.pop(context);
  }

  void _onSure() {
    Navigator.pop(context, true);
    // openAppSettings();
  }
}

class PermissionModel{
  final String? titleText;
  final String? sureText;
  final String? cancelText;

  const PermissionModel(
      {this.titleText, this.sureText, this.cancelText});
}
