
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

const _dialogTipText = "温馨提示";
const _dialogContentText = "迁移次数已达到上限,请联系技术人员解决,\n电话: ";
const _dialogBind = "我知道了";

void showTipsDialog(BuildContext context, String? phone) {
  showDialog(
      barrierDismissible: true, //为true点击蒙层退出
      context: context,
      useRootNavigator: false,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, state) {
          return Theme(
            data: ThemeData.light(),
            child: SimpleDialog(
                titlePadding: EdgeInsets.only(top: 16.w, bottom: 12.w),
                contentPadding:
                EdgeInsets.only(bottom: 0, top: 0, left: 0, right: 0),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.w)),
                title: Text(_dialogTipText,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontWeight: FontWeight.normal, fontSize: 17.sp)),
                children: [
                  Container(
                    padding: EdgeInsets.only(left: 16.w, right: 16.w),
                    height: 56.w,
                    child: RichText(
                      text: TextSpan(
                          text: _dialogContentText,
                        style: TextStyle(
                              color: Color.fromRGBO(0, 0, 0, 0.6), fontSize: 12.sp),
                        children: [
                          TextSpan(
                              text: "${phone ?? ""}", style: TextStyle(color: Color(0xff2283E2),decoration:TextDecoration.underline),
                              recognizer: TapGestureRecognizer()..onTap=(){
                                if(phone!=null&&phone.isNotEmpty){
                                  _makePhoneCall(phone);
                                }
                              },
                          ),
                        ]
                      ),
                    )
                  ),
                  SizedBox(
                    height: 1.w,
                    child: DecoratedBox(
                      decoration: BoxDecoration(color: Color.fromRGBO(0, 0, 0, 0.07)),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12.w)
                    ),
                    height: 46.w,
                    child: TextButton(
                      child: Center(
                        child: Text(_dialogBind,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                fontSize: 17.sp, color: Color(0xff2283E2))),
                      ),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  )),
                ]),
          );
        });
      });
}

Future<void> _makePhoneCall(String phone) async {
  // final Uri launchUri = Uri(
  //   scheme: 'tel',
  //   path: phone,
  // );
  await launch("tel:$phone");
}